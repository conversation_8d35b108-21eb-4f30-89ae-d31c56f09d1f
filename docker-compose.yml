version: '3.8'

services:
  instagram-analyzer-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      # Pass environment variables from .env file if needed
      - PYTHONUNBUFFERED=1
    volumes:
      # Uncomment for development to sync code changes without rebuilding
      # - ./app:/home/<USER>/app
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
