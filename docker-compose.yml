services:
  # Ollama service running DeepSeek models
  ollama:
    image: ollama/ollama:latest
    container_name: ollama-deepseek-vl
    ports:
      - "9006:11434"
    environment:
      - OLLAMA_HOST=0.0.0.0
      - DEEPSEEK_MODEL=deepseek-r1:14b  # Change this to use different models
    volumes:
      - ollama_data:/root/.ollama
      - ./ollama-startup.sh:/usr/local/bin/ollama-startup.sh
    command: ["/bin/sh", "/usr/local/bin/ollama-startup.sh"]
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 15
      start_period: 300s

  # Instagram Analyzer API service
  instagram-analyzer-api:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "9005:9005"
    environment:
      - PYTHONUNBUFFERED=1
      - OLLAMA_BASE_URL=http://ollama:11434
      - DEEPSEEK_MODEL=deepseek-r1:14b
    depends_on:
      - ollama
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  ollama_data:
