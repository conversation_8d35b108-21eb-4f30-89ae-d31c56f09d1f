#!/bin/bash

# Start Ollama server in the background
echo "Starting Ollama server..."
ollama serve &

# Wait for Ollama to be ready
echo "Waiting for Ollama server to be ready..."
while ! curl -s http://localhost:11434/api/tags > /dev/null; do
    echo "Waiting for Ollama server..."
    sleep 2
done

echo "Ollama server is ready!"

# Pull the LLaVA model
echo "Pulling llava:7b model..."
ollama pull llava:7b

echo "Model pulled successfully!"

# Keep the container running
wait
