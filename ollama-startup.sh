#!/bin/bash

# Start Ollama server in the background
echo "Starting Ollama server..."
ollama serve &

# Wait for Ollama to be ready
echo "Waiting for Ollama server to be ready..."
while ! curl -s http://localhost:11434/api/tags > /dev/null; do
    echo "Waiting for Ollama server..."
    sleep 2
done

echo "Ollama server is ready!"

# Check if we should use DeepSeek-VL2 (if available) or DeepSeek-R1
MODEL_NAME=${DEEPSEEK_MODEL:-"deepseek-r1:14b"}

echo "Pulling model: $MODEL_NAME..."

# Try to pull the specified model
if ollama pull "$MODEL_NAME"; then
    echo "Model $MODEL_NAME pulled successfully!"
else
    echo "Failed to pull $MODEL_NAME, falling back to deepseek-r1:14b..."
    ollama pull deepseek-r1:14b
    echo "Fallback model pulled successfully!"
fi

# Keep the container running
wait
