# Git
.git
.gitignore

# Docker
Dockerfile
.dockerignore

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
.venv/
env/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Documentation
*.md
docs/

# Tests
tests/
test_*.py
*_test.py

# Logs
*.log
logs/

# Environment variables (should be passed at runtime)
.env

# Temporary files
*.tmp
*.temp
