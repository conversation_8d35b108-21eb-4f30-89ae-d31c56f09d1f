"""
Ollama client management for the Instagram Service Provider API.

This module implements a singleton pattern for managing the Ollama client
connection to the DeepSeek-VL model running in a separate container.
"""

import ollama
import base64
import io
import os
from PIL import Image
from typing import List, Dict, Any, Optional
import logging
import httpx
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OllamaClient:
    """
    Singleton class for managing the Ollama client connection.

    This class handles communication with the Ollama service running
    the DeepSeek-VL model in a separate container.
    """

    _instance = None
    _initialized = False

    def __new__(cls):
        """Implement singleton pattern."""
        if cls._instance is None:
            cls._instance = super(OllamaClient, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the Ollama client if not already initialized."""
        if self._initialized:
            return

        logger.info("Initializing Ollama client for DeepSeek-R1 model...")

        # Configuration
        self.ollama_base_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
        self.model_name = os.getenv("DEEPSEEK_MODEL", "deepseek-r1:14b")
        self.max_retries = 5
        self.retry_delay = 10  # seconds

        # Initialize client
        self._setup_client()
        self._wait_for_model()

        self._initialized = True
        logger.info("Ollama client initialized successfully.")
    
    def _setup_client(self):
        """Setup the Ollama client with proper configuration."""
        try:
            # Configure the Ollama client
            self.client = ollama.Client(host=self.ollama_base_url)
            logger.info(f"Ollama client configured for: {self.ollama_base_url}")

        except Exception as e:
            logger.error(f"Failed to setup Ollama client: {e}")
            raise
    
    def _wait_for_model(self):
        """
        Wait for the Ollama service to be ready and the model to be available.
        """
        logger.info("Waiting for Ollama service and model to be ready...")

        for attempt in range(self.max_retries):
            try:
                # Check if Ollama service is responding
                response = self.client.list()
                logger.info("Ollama service is responding")

                # Check if our model is available
                models = response.get('models', [])
                available_models = []
                for model in models:
                    if isinstance(model, dict):
                        # Try different possible keys for the model name
                        model_name = model.get('name') or model.get('model')
                        if model_name:
                            available_models.append(model_name)
                    else:
                        # Handle case where model is returned as an object
                        model_str = str(model)
                        if 'model=' in model_str:
                            # Extract model name from string like "model='llava:7b'"
                            import re
                            match = re.search(r"model='([^']+)'", model_str)
                            if match:
                                available_models.append(match.group(1))
                        else:
                            available_models.append(model_str)

                if self.model_name in available_models:
                    logger.info(f"Model {self.model_name} is available")
                    self._test_model()
                    return
                else:
                    logger.info(f"Model {self.model_name} not found. Available models: {available_models}")
                    logger.info("Waiting for model to be pulled...")

            except Exception as e:
                logger.warning(f"Attempt {attempt + 1}/{self.max_retries} failed: {e}")

            if attempt < self.max_retries - 1:
                logger.info(f"Retrying in {self.retry_delay} seconds...")
                time.sleep(self.retry_delay)

        raise Exception(f"Failed to connect to Ollama service or model {self.model_name} after {self.max_retries} attempts")

    def _test_model(self):
        """Test the model with a simple request to ensure it's working."""
        try:
            logger.info("Testing model with a simple request...")

            # Create a small test image
            test_image = Image.new('RGB', (100, 100), color='red')

            # Convert to base64
            buffered = io.BytesIO()
            test_image.save(buffered, format="PNG")
            img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')

            # Test the model
            response = self.client.generate(
                model=self.model_name,
                prompt="What color is this image?",
                images=[img_base64],
                options={'num_predict': 10}
            )

            logger.info("Model test successful")

        except Exception as e:
            logger.warning(f"Model test failed: {e}")

    def generate_response(self, prompt: str, image: Image.Image) -> str:
        """
        Generate a response using the Ollama model.

        Args:
            prompt: The text prompt for the model
            image: PIL Image to analyze

        Returns:
            Generated response text
        """
        try:
            # Convert image to base64
            buffered = io.BytesIO()
            image.save(buffered, format="PNG")
            img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')

            # Generate response
            response = self.client.generate(
                model=self.model_name,
                prompt=prompt,
                images=[img_base64],
                options={
                    'num_predict': 1024,
                    'temperature': 0.1,
                    'top_p': 0.9
                }
            )

            return response['response']

        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            raise

    def is_ready(self) -> bool:
        """Check if the Ollama client is ready."""
        return self._initialized

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model."""
        try:
            models = self.client.list()
            for model in models.get('models', []):
                if model['name'] == self.model_name:
                    return model
            return {}
        except Exception:
            return {}


# Create a global singleton instance
# This will be imported by other modules to access the Ollama client
ollama_client = OllamaClient()
