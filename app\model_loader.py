"""
Model loading and management for the Instagram Service Provider API.

This module implements a singleton pattern for loading and managing the DeepSeek-VL
vision-language model with optimizations for GPU inference.
"""

import torch
from transformers import AutoModelForCausalLM, BitsAndBytesConfig
from deepseek_vl.models import VLChatProcessor, MultiModalityCausalLM
from deepseek_vl.utils.io import load_pil_images
from PIL import Image
import io
from typing import List, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ModelLoader:
    """
    Singleton class for loading and managing the DeepSeek-VL model.
    
    This class ensures that the large vision-language model is loaded only once
    at application startup, reducing memory usage and improving response times.
    """
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        """Implement singleton pattern."""
        if cls._instance is None:
            cls._instance = super(ModelLoader, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize the model loader if not already initialized."""
        if self._initialized:
            return
            
        logger.info("Initializing ModelLoader and loading DeepSeek-VL model...")
        
        # Model configuration
        self.model_path = "deepseek-ai/deepseek-vl-7b-chat"
        
        # Load model and processor
        self._load_model()
        self._warmup_model()
        
        self._initialized = True
        logger.info("Model loaded and warmed up successfully.")
    
    def _load_model(self):
        """Load the DeepSeek-VL model with optimizations."""
        try:
            # Configure 4-bit quantization to reduce VRAM usage
            quantization_config = BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_quant_type="nf4",  # Normal Float 4 for optimal performance
                bnb_4bit_compute_dtype=torch.bfloat16,  # Use bfloat16 for computation
            )
            
            # Load the VL Chat Processor (handles tokenization and image processing)
            logger.info("Loading VL Chat Processor...")
            self.vl_chat_processor: VLChatProcessor = VLChatProcessor.from_pretrained(
                self.model_path
            )
            self.tokenizer = self.vl_chat_processor.tokenizer
            
            # Load the model with quantization and Flash Attention 2
            logger.info("Loading model with 4-bit quantization and Flash Attention 2...")
            self.model: MultiModalityCausalLM = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                quantization_config=quantization_config,
                attn_implementation="flash_attention_2",  # Enable Flash Attention 2
                torch_dtype=torch.bfloat16,
                device_map="auto"  # Automatically distribute across available GPUs
            )
            
            # Set model to evaluation mode
            self.model.eval()
            
            logger.info(f"Model loaded successfully on device: {self.model.device}")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def _warmup_model(self):
        """
        Warm up the model with a dummy inference to pre-compile CUDA kernels.
        
        This eliminates the "cold start" latency for the first real inference.
        """
        logger.info("Warming up the model...")
        try:
            # Create a dummy image and conversation
            dummy_image = Image.new('RGB', (1024, 1024), color='red')
            dummy_conversation = [
                {
                    "role": "User", 
                    "content": "<image_placeholder>test", 
                    "images": ["dummy"]
                }
            ]
            
            with torch.no_grad():
                # Prepare inputs using the VL Chat Processor
                prepare_inputs = self.vl_chat_processor(
                    conversations=dummy_conversation,
                    images=[dummy_image],
                    force_batchify=True
                ).to(self.model.device)
                
                # Prepare input embeddings
                inputs_embeds = self.model.prepare_inputs_embeds(**prepare_inputs)
                
                # Run a short generation to warm up CUDA kernels
                self.model.language_model.generate(
                    inputs_embeds=inputs_embeds,
                    attention_mask=prepare_inputs.attention_mask,
                    pad_token_id=self.tokenizer.eos_token_id,
                    max_new_tokens=5,  # Very short generation for warmup
                    do_sample=False
                )
                
            logger.info("Model warm-up completed successfully.")
            
        except Exception as e:
            logger.warning(f"Model warm-up failed (this may not affect functionality): {e}")
    
    def get_model(self):
        """Get the loaded model instance."""
        return self.model
    
    def get_processor(self):
        """Get the VL Chat Processor instance."""
        return self.vl_chat_processor
    
    def get_tokenizer(self):
        """Get the tokenizer instance."""
        return self.tokenizer


# Create a global singleton instance
# This will be imported by other modules to access the pre-loaded model
model_loader = ModelLoader()
