# Stage 1: Build the environment with all dependencies
FROM nvidia/cuda:12.1.1-devel-ubuntu22.04 AS builder

# Prevent interactive prompts during installation
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# Install system dependencies and Python
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    python3.10 \
    python3.10-venv \
    python3-pip \
    build-essential \
    git && \
    rm -rf /var/lib/apt/lists/*

# Create and activate a virtual environment
RUN python3.10 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Upgrade pip and install wheel
RUN python -m pip install --upgrade pip wheel

# Copy requirements and install packages
# This caches the layer with installed packages
COPY requirements.txt .
RUN python -m pip install --no-cache-dir -r requirements.txt

# Stage 2: Create the final, lean production image
FROM nvidia/cuda:12.1.1-base-ubuntu22.04

# Set non-interactive frontend
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# Create a non-root user for security
RUN useradd --create-home appuser
WORKDIR /home/<USER>

# Copy the virtual environment from the builder stage
COPY --from=builder /opt/venv /opt/venv

# Copy the application code
COPY --chown=appuser:appuser ./app ./app

# Set the PATH to use the venv
ENV PATH="/opt/venv/bin:$PATH"

# Switch to the non-root user
USER appuser

# Expose the port the app runs on
EXPOSE 8000

# Define the command to run the application
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
