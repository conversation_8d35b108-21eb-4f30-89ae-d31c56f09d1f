# DeepSeek-VL2 Setup Guide

## Current Status

**⚠️ Important Note**: DeepSeek-VL2 is not yet available in the official Ollama library. The system is currently configured to use **DeepSeek-R1 14B** as the closest available alternative.

## Available Models

### Currently Used: DeepSeek-R1 14B
- **Model**: `deepseek-r1:14b`
- **Parameters**: 14 billion
- **Capabilities**: Advanced reasoning, some vision capabilities
- **Status**: ✅ Available in Ollama

### Target Model: DeepSeek-VL2 27.5B
- **Model**: `deepseek-vl2:27.5b` (not yet available)
- **Parameters**: 27.5 billion
- **Capabilities**: Advanced vision-language understanding
- **Status**: ❌ Not available in Ollama yet

## How to Switch Models

### Option 1: Use Different Available Models

You can change the model by updating the `DEEPSEEK_MODEL` environment variable in `docker-compose.yml`:

```yaml
environment:
  - DEEPSEEK_MODEL=deepseek-r1:32b  # For larger model
  # or
  - DEEPSEEK_MODEL=deepseek-r1:7b   # For smaller model
```

Available DeepSeek models in Ollama:
- `deepseek-r1:1.5b` - Smallest, fastest
- `deepseek-r1:7b` - Good balance
- `deepseek-r1:8b` - Slightly larger
- `deepseek-r1:14b` - **Currently used**
- `deepseek-r1:32b` - Larger, more capable
- `deepseek-r1:70b` - Very large, requires significant resources

### Option 2: When DeepSeek-VL2 Becomes Available

Once DeepSeek-VL2 is added to Ollama's library, you can switch by:

1. Update the environment variable:
```yaml
environment:
  - DEEPSEEK_MODEL=deepseek-vl2:27.5b
```

2. Restart the containers:
```bash
docker-compose down
docker-compose up -d
```

### Option 3: Custom Model Import (Advanced)

If you have access to DeepSeek-VL2 in GGUF format, you can:

1. Place the GGUF file in a accessible location
2. Update the `Modelfile.deepseek-vl2` with the correct path
3. Create the model in Ollama:
```bash
docker exec ollama-deepseek-vl ollama create deepseek-vl2:27.5b -f /path/to/Modelfile.deepseek-vl2
```

## Performance Considerations

### DeepSeek-R1 14B (Current)
- **RAM**: ~8-12 GB
- **VRAM**: ~8-10 GB (with GPU)
- **Speed**: Fast inference
- **Quality**: Good reasoning, limited vision

### DeepSeek-VL2 27.5B (Target)
- **RAM**: ~16-20 GB
- **VRAM**: ~16-20 GB (with GPU)
- **Speed**: Slower inference
- **Quality**: Excellent vision-language understanding

## Monitoring Model Status

Check which model is currently loaded:
```bash
curl http://localhost:9006/api/tags
```

Check API health with current model:
```bash
curl http://localhost:9005/health
```

## Troubleshooting

### Model Not Found
If you get "model not found" errors:
1. Check available models: `docker exec ollama-deepseek-vl ollama list`
2. Pull the model manually: `docker exec ollama-deepseek-vl ollama pull deepseek-r1:14b`
3. Restart containers: `docker-compose restart`

### Out of Memory
If you encounter memory issues:
1. Use a smaller model (e.g., `deepseek-r1:7b`)
2. Ensure sufficient system RAM/VRAM
3. Close other applications

### Performance Issues
For better performance:
1. Use GPU acceleration (ensure NVIDIA drivers are installed)
2. Increase Docker memory limits
3. Use SSD storage for model files

## Future Updates

This setup is designed to be forward-compatible. When DeepSeek-VL2 becomes available in Ollama:
1. The system will automatically support it
2. Simply change the environment variable
3. No code changes required

## Links

- [DeepSeek-VL2 on Hugging Face](https://huggingface.co/deepseek-ai/deepseek-vl2)
- [Ollama DeepSeek Models](https://ollama.com/search?q=deepseek)
- [GitHub Issue for DeepSeek-VL2 Support](https://github.com/ollama/ollama/issues/8096)
