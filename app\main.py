"""
FastAPI application for Instagram Service Provider Analysis.

This module implements the main FastAPI application that provides an endpoint
for analyzing Instagram profile screenshots using the DeepSeek-VL model.
"""

from fastapi import FastAPI, File, Form, UploadFile, HTTPException, Depends
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from PIL import Image
import io
import json
import logging
from typing import List, Dict, Any

from .model_loader import ollama_client
from .schemas import InstagramProfileAnalysis, AnalysisRequest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI application instance with extensive documentation
app = FastAPI(
    title="Instagram Service Provider Analysis API",
    description="""
    ## 🔍 Instagram Service Provider Analysis API

    A production-grade API powered by **DeepSeek-VL** vision-language model for analyzing Instagram profile screenshots
    to identify service providers and extract structured business information.

    ### 🚀 Key Features

    - **AI-Powered Analysis**: Uses state-of-the-art vision-language model (DeepSeek-VL-7B-Chat)
    - **Service Provider Detection**: Automatically identifies if an Instagram profile belongs to a service provider
    - **Structured Data Extraction**: Extracts key business information in JSON format
    - **GPU-Accelerated**: Optimized for NVIDIA GPU inference with 4-bit quantization
    - **Production Ready**: Comprehensive error handling, logging, and health monitoring

    ### 📊 What We Extract

    - **Username**: Instagram handle (e.g., @beautysalon_la)
    - **Location**: Business location or service area
    - **Contact Information**: Email addresses and booking links
    - **Services Offered**: List of specific services provided
    - **Provider Type**: Classification as individual or business

    ### 🎯 Use Cases

    - **Market Research**: Identify service providers in specific niches
    - **Lead Generation**: Extract contact information from Instagram profiles
    - **Competitive Analysis**: Analyze service offerings in your market
    - **Data Collection**: Build databases of service providers

    ### 🔧 Technical Details

    - **Model**: DeepSeek-VL-7B-Chat with 4-bit quantization
    - **Input**: Instagram profile screenshots (PNG, JPG, JPEG)
    - **Output**: Structured JSON with extracted information
    - **Performance**: ~2-5 seconds per analysis (after warm-up)

    ### 📝 Getting Started

    1. **Health Check**: Use `/health` to verify API status
    2. **Analyze Profile**: Upload an image to `/analyze` with optional instructions
    3. **Review Results**: Get structured JSON response with extracted data

    ### 🛡️ Error Handling

    The API provides detailed error messages for:
    - Invalid file formats
    - Model inference failures
    - JSON parsing errors
    - GPU/memory issues

    ### 📚 Example Response

    ```json
    {
      "username": "@beautystudio_la",
      "location": "Los Angeles, CA",
      "email": "<EMAIL>",
      "booking_link": "https://beautystudiola.com/book",
      "services_offered": ["Lash Extensions", "Microblading", "Brow Lamination"],
      "provider_type": "business"
    }
    ```
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    contact={
        "name": "Instagram Analyzer API Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    servers=[
        {
            "url": "http://localhost:9005",
            "description": "Local development server"
        },
        {
            "url": "https://api.instagram-analyzer.com",
            "description": "Production server"
        }
    ]
)

# Add CORS middleware for cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def get_ollama_client():
    """Dependency to get the Ollama client."""
    return ollama_client


def preprocess_image(image_bytes: bytes) -> Image.Image:
    """
    Preprocess uploaded image for model input.
    
    Args:
        image_bytes: Raw image bytes from uploaded file
        
    Returns:
        PIL Image object resized to 1024x1024 and converted to RGB
    """
    try:
        # Open image from bytes
        image = Image.open(io.BytesIO(image_bytes))
        
        # Ensure RGB format (removes alpha channel if present)
        if image.mode != "RGB":
            image = image.convert("RGB")
        
        # Resize to model's expected input size
        processed_image = image.resize((1024, 1024))
        
        return processed_image
        
    except Exception as e:
        logger.error(f"Image preprocessing failed: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to process image: {str(e)}")


def construct_analysis_prompt(instructions: str) -> str:
    """
    Construct the prompt for Instagram profile analysis using few-shot learning.

    Args:
        instructions: Custom instructions for the analysis

    Returns:
        Formatted prompt string for the Ollama model
    """
    # Few-shot prompting strategy with examples for better reliability
    prompt = f"""You are an expert analyst tasked with extracting structured information from Instagram profile screenshots.
Your goal is to identify if the account is a service provider and extract key details.
Analyze the user's bio and any visible posts in the provided image.

**Example 1:**
*Input Image shows a profile for a lash artist.*
**Output JSON:**
```json
{{
    "username": "@lashbylily",
    "location": "Miami, FL",
    "email": "<EMAIL>",
    "booking_link": "https://lashbylily.booksy.com",
    "services_offered": ["Classic Lashes", "Volume Lashes", "Hybrid Lashes"],
    "provider_type": "individual"
}}
```

**Example 2:**
*Input Image shows a profile for a generic travel blogger.*
**Output JSON:**
```json
{{
    "username": "@traveling_tom",
    "location": null,
    "email": null,
    "booking_link": null,
    "services_offered": [],
    "provider_type": "individual"
}}
```

Based on these examples, analyze the following image and instructions.
Provide your response as a valid JSON object only, with no additional text or explanations.

Instructions: {instructions}"""

    return prompt


def run_model_inference(
    image: Image.Image,
    instructions: str,
    client
) -> str:
    """
    Run inference using the Ollama client.

    Args:
        image: Preprocessed PIL Image
        instructions: Analysis instructions
        client: Ollama client instance

    Returns:
        Generated text response from the model
    """
    try:
        # Construct the prompt
        prompt = construct_analysis_prompt(instructions)

        # Generate response using Ollama
        response_text = client.generate_response(prompt, image)

        return response_text

    except Exception as e:
        logger.error(f"Model inference failed: {e}")
        raise HTTPException(status_code=500, detail=f"Model inference failed: {str(e)}")


def extract_json_from_response(response_text: str) -> Dict[str, Any]:
    """
    Extract and parse JSON from model response.
    
    Args:
        response_text: Raw text response from the model
        
    Returns:
        Parsed JSON dictionary
    """
    try:
        # Find JSON boundaries in the response
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1
        
        if json_start == -1 or json_end == 0:
            raise ValueError("No JSON object found in response")
        
        # Extract JSON substring
        json_str = response_text[json_start:json_end]
        
        # Parse JSON
        parsed_json = json.loads(json_str)
        
        return parsed_json
        
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"JSON extraction failed: {e}")
        logger.error(f"Raw response: {response_text}")
        raise HTTPException(
            status_code=500, 
            detail="Failed to parse valid JSON from model response"
        )


@app.get(
    "/",
    tags=["Root"],
    summary="API Welcome Message",
    description="Returns welcome message and links to API documentation"
)
async def read_root():
    """
    ## Welcome to Instagram Service Provider Analysis API

    This endpoint provides basic information about the API and links to documentation.

    ### Quick Links
    - **API Documentation**: `/docs` - Interactive Swagger UI
    - **Alternative Docs**: `/redoc` - ReDoc documentation
    - **Health Check**: `/health` - System status and GPU information
    - **Analysis Endpoint**: `/analyze` - Main analysis functionality
    """
    return {
        "message": "Welcome to the Instagram Service Provider Analysis API",
        "version": "1.0.0",
        "documentation": {
            "swagger_ui": "/docs",
            "redoc": "/redoc"
        },
        "endpoints": {
            "health_check": "/health",
            "analyze_profile": "/analyze"
        },
        "status": "operational"
    }


@app.get(
    "/health",
    tags=["Health"],
    summary="System Health Check",
    description="Comprehensive health check including GPU status, model loading status, and system information"
)
async def health_check():
    """
    ## System Health Check

    This endpoint provides detailed information about the system status, including:

    ### Checked Components
    - **API Status**: Overall application health
    - **GPU Availability**: CUDA and GPU detection
    - **Model Status**: DeepSeek-VL model loading status
    - **Memory Information**: GPU memory usage (if available)

    ### Response Codes
    - **200**: System is healthy and ready
    - **500**: System has issues (check error details)

    ### Usage
    Use this endpoint to verify the API is ready before sending analysis requests.
    It's recommended to call this endpoint after container startup to ensure
    the model has finished loading.
    """
    try:
        # Check Ollama client status
        ollama_ready = ollama_client.is_ready()
        model_info = ollama_client.get_model_info() if ollama_ready else {}

        return {
            "status": "healthy" if ollama_ready else "initializing",
            "system_info": {
                "ollama_client_ready": ollama_ready,
                "ollama_base_url": ollama_client.ollama_base_url,
                "model_name": ollama_client.model_name,
                "model_info": model_info
            },
            "api_info": {
                "version": "1.0.0",
                "port": 9005,
                "endpoints_available": ["/", "/health", "/analyze", "/docs", "/redoc"]
            }
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "unhealthy",
                "error": str(e),
                "message": "System health check failed"
            }
        )


@app.post(
    "/analyze",
    response_model=InstagramProfileAnalysis,
    summary="🔍 Analyze Instagram Profile Screenshot",
    description="""
    **Analyze Instagram profile screenshots using AI to identify service providers and extract business information.**

    This endpoint uses the DeepSeek-VL vision-language model to analyze Instagram profile screenshots
    and determine if they belong to service providers (beauty, wellness, coaching, etc.), then extracts
    key business information in a structured JSON format.
    """,
    tags=["Analysis"],
    responses={
        200: {
            "description": "Successful analysis with extracted information",
            "content": {
                "application/json": {
                    "example": {
                        "username": "@beautystudio_la",
                        "location": "Los Angeles, CA",
                        "email": "<EMAIL>",
                        "booking_link": "https://beautystudiola.com/book",
                        "services_offered": ["Lash Extensions", "Microblading", "Brow Lamination"],
                        "provider_type": "business"
                    }
                }
            }
        },
        400: {
            "description": "Bad Request - Invalid file type or format",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Invalid file type. Only image files are allowed."
                    }
                }
            }
        },
        500: {
            "description": "Internal Server Error - Model inference or processing failed",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Model inference failed: CUDA out of memory"
                    }
                }
            }
        }
    }
)
async def analyze_profile(
    image: UploadFile = File(
        ...,
        description="""
        **Instagram profile screenshot to analyze**

        ### Supported Formats
        - PNG, JPG, JPEG
        - Maximum file size: 10MB (recommended)
        - Minimum resolution: 500x500 pixels
        - Optimal resolution: 1080x1350 pixels (Instagram standard)

        ### Best Practices
        - Ensure the profile bio is clearly visible
        - Include the username and any visible posts
        - Avoid heavily cropped or blurry images
        - Screenshots should show the main profile page

        ### What to Include
        - Profile picture and username
        - Bio text with services/contact info
        - Location information (if visible)
        - Any visible posts showing services
        """,
        example="profile_screenshot.png"
    ),
    instructions: str = Form(
        default="Analyze this Instagram profile to determine if it's a service provider and extract relevant business information.",
        description="""
        **Custom instructions for the analysis**

        ### Default Behavior
        The API automatically looks for service provider indicators and extracts:
        - Username and contact information
        - Services offered
        - Location and booking details
        - Provider type classification

        ### Custom Instructions Examples
        - "Focus on beauty and wellness services only"
        - "Extract contact information and services for a hair salon"
        - "Identify if this is a fitness coach or personal trainer"
        - "Look for booking links and service pricing"

        ### Tips for Better Results
        - Be specific about the type of services you're looking for
        - Mention if you want focus on particular information
        - Use clear, descriptive language
        - Avoid overly complex or contradictory instructions
        """,
        example="Analyze this profile for beauty and wellness services, focusing on contact information and booking details."
    ),
    client = Depends(get_ollama_client)
):
    """
    ## 🎯 Analyze Instagram Profile Screenshot

    This endpoint is the core functionality of the API. It analyzes Instagram profile screenshots
    using advanced AI to identify service providers and extract structured business information.

    ### 🔄 Process Flow

    1. **Image Upload**: Accepts Instagram profile screenshot
    2. **Preprocessing**: Resizes and optimizes image for AI analysis
    3. **AI Analysis**: Uses DeepSeek-VL model to analyze the profile
    4. **Information Extraction**: Extracts structured business data
    5. **Validation**: Ensures response matches expected schema
    6. **Response**: Returns JSON with extracted information

    ### 📊 Extracted Information

    | Field | Type | Description | Example |
    |-------|------|-------------|---------|
    | `username` | string | Instagram handle | "@beautysalon_la" |
    | `location` | string/null | Business location | "Los Angeles, CA" |
    | `email` | string/null | Contact email | "<EMAIL>" |
    | `booking_link` | string/null | Booking URL | "https://salon.com/book" |
    | `services_offered` | array | List of services | ["Facials", "Massage"] |
    | `provider_type` | string | "individual" or "business" | "business" |

    ### ⚡ Performance

    - **Cold Start**: ~10-15 seconds (first request after startup)
    - **Warm Requests**: ~2-5 seconds per analysis
    - **Concurrent Requests**: Supported (queued processing)
    - **Memory Usage**: ~5-6GB GPU VRAM

    ### 🎯 Use Cases

    - **Lead Generation**: Extract contact info from Instagram profiles
    - **Market Research**: Identify competitors and their services
    - **Data Collection**: Build databases of service providers
    - **Competitive Analysis**: Analyze service offerings and pricing

    ### 💡 Tips for Best Results

    1. **Image Quality**: Use high-resolution, clear screenshots
    2. **Complete Profiles**: Include bio, username, and visible posts
    3. **Specific Instructions**: Provide clear, focused instructions
    4. **Service Types**: Works best with beauty, wellness, fitness, coaching

    ### 🚨 Error Handling

    The API provides detailed error messages for common issues:
    - Invalid file formats
    - Model processing failures
    - GPU memory issues
    - Network timeouts

    ### 📝 Example Request

    ```bash
    curl -X POST "http://localhost:9005/analyze" \\
      -H "accept: application/json" \\
      -F "image=@profile_screenshot.png" \\
      -F "instructions=Analyze for beauty services and contact info"
    ```
    """
    # Validate file type
    if not image.content_type or not image.content_type.startswith("image/"):
        raise HTTPException(
            status_code=400, 
            detail="Invalid file type. Only image files are allowed."
        )
    
    try:
        # Read and preprocess the image
        image_bytes = await image.read()
        processed_image = preprocess_image(image_bytes)
        
        # Run model inference
        response_text = run_model_inference(processed_image, instructions, client)
        
        # Extract and parse JSON from response
        result_dict = extract_json_from_response(response_text)
        
        # Validate and return structured response
        validated_result = InstagramProfileAnalysis.model_validate(result_dict)
        
        logger.info(f"Successfully analyzed profile: {validated_result.username}")
        return validated_result
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during analysis: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"An unexpected error occurred during analysis: {str(e)}"
        )
