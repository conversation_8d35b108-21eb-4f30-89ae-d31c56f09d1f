"""
FastAPI application for Instagram Service Provider Analysis.

This module implements the main FastAPI application that provides an endpoint
for analyzing Instagram profile screenshots using the DeepSeek-VL model.
"""

from fastapi import FastAPI, File, Form, UploadFile, HTTPException, Depends
from fastapi.responses import J<PERSON><PERSON>esponse
from fastapi.middleware.cors import CORSMiddleware
from PIL import Image
import io
import json
import torch
import logging
from typing import List, Dict, Any

from .model_loader import model_loader
from .schemas import InstagramProfileAnalysis, AnalysisRequest

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI application instance
app = FastAPI(
    title="Instagram Service Provider Analysis API",
    description="An API to analyze Instagram profile screenshots using DeepSeek-VL to identify service providers and extract key business information.",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware for cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def get_model_components():
    """Dependency to get model components from the singleton loader."""
    return {
        "model": model_loader.get_model(),
        "processor": model_loader.get_processor(),
        "tokenizer": model_loader.get_tokenizer()
    }


def preprocess_image(image_bytes: bytes) -> Image.Image:
    """
    Preprocess uploaded image for model input.
    
    Args:
        image_bytes: Raw image bytes from uploaded file
        
    Returns:
        PIL Image object resized to 1024x1024 and converted to RGB
    """
    try:
        # Open image from bytes
        image = Image.open(io.BytesIO(image_bytes))
        
        # Ensure RGB format (removes alpha channel if present)
        if image.mode != "RGB":
            image = image.convert("RGB")
        
        # Resize to model's expected input size
        processed_image = image.resize((1024, 1024))
        
        return processed_image
        
    except Exception as e:
        logger.error(f"Image preprocessing failed: {e}")
        raise HTTPException(status_code=400, detail=f"Failed to process image: {str(e)}")


def construct_analysis_prompt(instructions: str) -> List[Dict[str, Any]]:
    """
    Construct the prompt for Instagram profile analysis using few-shot learning.
    
    Args:
        instructions: Custom instructions for the analysis
        
    Returns:
        List containing the conversation structure for the model
    """
    # Few-shot prompting strategy with examples for better reliability
    prompt = f"""
You are an expert analyst tasked with extracting structured information from Instagram profile screenshots.
Your goal is to identify if the account is a service provider and extract key details.
Analyze the user's bio and any visible posts in the provided image.

**Example 1:**
*Input Image shows a profile for a lash artist.*
**Output JSON:**
```json
{{
    "username": "@lashbylily",
    "location": "Miami, FL",
    "email": "<EMAIL>",
    "booking_link": "https://lashbylily.booksy.com",
    "services_offered": ["Classic Lashes", "Volume Lashes", "Hybrid Lashes"],
    "provider_type": "individual"
}}
```

**Example 2:**
*Input Image shows a profile for a generic travel blogger.*
**Output JSON:**
```json
{{
    "username": "@traveling_tom",
    "location": null,
    "email": null,
    "booking_link": null,
    "services_offered": [],
    "provider_type": "individual"
}}
```

Based on these examples, analyze the following image and instructions.
Provide your response as a valid JSON object only, with no additional text or explanations.

Instructions: {instructions}
"""
    
    return [{"role": "User", "content": f"<image_placeholder>{prompt}", "images": ["input_image"]}]


def run_model_inference(
    image: Image.Image, 
    instructions: str, 
    model_components: Dict[str, Any]
) -> str:
    """
    Run inference on the DeepSeek-VL model.
    
    Args:
        image: Preprocessed PIL Image
        instructions: Analysis instructions
        model_components: Dictionary containing model, processor, and tokenizer
        
    Returns:
        Generated text response from the model
    """
    try:
        model = model_components["model"]
        processor = model_components["processor"]
        tokenizer = model_components["tokenizer"]
        
        # Construct the conversation prompt
        conversation = construct_analysis_prompt(instructions)
        
        # Prepare model inputs
        with torch.no_grad():
            prepare_inputs = processor(
                conversations=conversation,
                images=[image],
                force_batchify=True
            ).to(model.device)
            
            # Prepare input embeddings
            inputs_embeds = model.prepare_inputs_embeds(**prepare_inputs)
            
            # Generate response
            outputs = model.language_model.generate(
                inputs_embeds=inputs_embeds,
                attention_mask=prepare_inputs.attention_mask,
                pad_token_id=tokenizer.eos_token_id,
                bos_token_id=tokenizer.bos_token_id,
                eos_token_id=tokenizer.eos_token_id,
                max_new_tokens=1024,
                do_sample=False,  # Deterministic generation
                use_cache=True
            )
            
        # Decode the generated tokens
        response_text = tokenizer.decode(outputs[0].cpu().tolist(), skip_special_tokens=True)
        
        return response_text
        
    except Exception as e:
        logger.error(f"Model inference failed: {e}")
        raise HTTPException(status_code=500, detail=f"Model inference failed: {str(e)}")


def extract_json_from_response(response_text: str) -> Dict[str, Any]:
    """
    Extract and parse JSON from model response.
    
    Args:
        response_text: Raw text response from the model
        
    Returns:
        Parsed JSON dictionary
    """
    try:
        # Find JSON boundaries in the response
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1
        
        if json_start == -1 or json_end == 0:
            raise ValueError("No JSON object found in response")
        
        # Extract JSON substring
        json_str = response_text[json_start:json_end]
        
        # Parse JSON
        parsed_json = json.loads(json_str)
        
        return parsed_json
        
    except (json.JSONDecodeError, ValueError) as e:
        logger.error(f"JSON extraction failed: {e}")
        logger.error(f"Raw response: {response_text}")
        raise HTTPException(
            status_code=500, 
            detail="Failed to parse valid JSON from model response"
        )


@app.get("/", tags=["Root"])
async def read_root():
    """Root endpoint providing API information."""
    return {
        "message": "Welcome to the Instagram Service Provider Analysis API",
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint to verify API and GPU availability."""
    try:
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0
        
        return {
            "status": "healthy",
            "gpu_available": gpu_available,
            "gpu_count": gpu_count,
            "model_loaded": model_loader._initialized
        }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"status": "unhealthy", "error": str(e)}
        )


@app.post(
    "/analyze",
    response_model=InstagramProfileAnalysis,
    summary="Analyze Instagram Profile Screenshot",
    description="Analyzes an Instagram profile screenshot to identify service providers and extract key business information.",
    tags=["Analysis"]
)
async def analyze_profile(
    image: UploadFile = File(..., description="Instagram profile screenshot to analyze"),
    instructions: str = Form(
        default="Analyze this Instagram profile to determine if it's a service provider and extract relevant business information.",
        description="Custom instructions for the analysis"
    ),
    model_components: Dict[str, Any] = Depends(get_model_components)
):
    """
    Analyze an Instagram profile screenshot to identify service providers.
    
    This endpoint accepts an image file and optional instructions, then uses the
    DeepSeek-VL model to analyze the profile and extract structured information
    about potential service providers.
    """
    # Validate file type
    if not image.content_type or not image.content_type.startswith("image/"):
        raise HTTPException(
            status_code=400, 
            detail="Invalid file type. Only image files are allowed."
        )
    
    try:
        # Read and preprocess the image
        image_bytes = await image.read()
        processed_image = preprocess_image(image_bytes)
        
        # Run model inference
        response_text = run_model_inference(processed_image, instructions, model_components)
        
        # Extract and parse JSON from response
        result_dict = extract_json_from_response(response_text)
        
        # Validate and return structured response
        validated_result = InstagramProfileAnalysis.model_validate(result_dict)
        
        logger.info(f"Successfully analyzed profile: {validated_result.username}")
        return validated_result
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during analysis: {e}")
        raise HTTPException(
            status_code=500, 
            detail=f"An unexpected error occurred during analysis: {str(e)}"
        )
