"""
Pydantic schemas for the Instagram Service Provider API.

This module defines the data models used for request/response validation
and API documentation generation.
"""

from pydantic import BaseModel, EmailStr, HttpUrl, Field
from typing import List, Optional, Literal


class InstagramProfileAnalysis(BaseModel):
    """
    Pydantic model representing the structured output of Instagram profile analysis.
    
    This model defines the expected structure for the API's response when analyzing
    Instagram profile screenshots to identify service providers and extract key information.
    """
    
    username: str = <PERSON>(
        ..., 
        description="The Instagram username (e.g., @beautystudio_la)",
        example="@beautystudio_la"
    )
    
    location: Optional[str] = Field(
        None, 
        description="City, State, or general area mentioned in the bio",
        example="Los Angeles, CA"
    )
    
    email: Optional[EmailStr] = Field(
        None, 
        description="Contact email address, if available",
        example="<EMAIL>"
    )
    
    booking_link: Optional[HttpUrl] = Field(
        None, 
        description="The URL for booking appointments or services",
        example="https://beautystudiola.com/book"
    )
    
    services_offered: List[str] = Field(
        default_factory=list,
        description="A list of specific services offered by the provider",
        example=["Lash Extensions", "Microblading", "Brow Lamination"]
    )
    
    provider_type: Literal["individual", "business"] = Field(
        ...,
        description="Classification of the service provider type",
        example="business"
    )

    class Config:
        """Pydantic model configuration."""
        from_attributes = True
        json_schema_extra = {
            "example": {
                "username": "@beautystudio_la",
                "location": "Los Angeles, CA", 
                "email": "<EMAIL>",
                "booking_link": "https://beautystudiola.com/book",
                "services_offered": ["Lash Extensions", "Microblading", "Brow Lamination"],
                "provider_type": "business"
            }
        }


class AnalysisRequest(BaseModel):
    """
    Pydantic model for analysis request parameters.
    
    This model can be used for additional request validation if needed.
    """
    
    instructions: str = Field(
        ...,
        description="Custom instructions for the analysis",
        example="Analyze this profile for beauty and wellness services."
    )
