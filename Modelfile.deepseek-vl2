# Modelfile for DeepSeek-VL2
# This is a template for when DeepSeek-VL2 becomes available in GGUF format

# Note: This model is not yet available in Ollama's official library
# You would need to:
# 1. Convert the HuggingFace model to GGUF format using llama.cpp
# 2. Place the GGUF file in the appropriate location
# 3. Use this Modelfile to create the model in Ollama

# FROM /path/to/deepseek-vl2-27.5b.gguf

TEMPLATE """{{ if .System }}<|im_start|>system
{{ .System }}<|im_end|>
{{ end }}{{ if .Prompt }}<|im_start|>user
{{ .Prompt }}<|im_end|>
{{ end }}<|im_start|>assistant
{{ .Response }}<|im_end|>
"""

PARAMETER stop "<|im_start|>"
PARAMETER stop "<|im_end|>"
PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1

SYSTEM """You are DeepSeek-VL2, an advanced vision-language model. You can analyze images and provide detailed descriptions, answer questions about visual content, and extract information from images. You are particularly good at understanding Instagram profiles, business information, and service provider details."""
