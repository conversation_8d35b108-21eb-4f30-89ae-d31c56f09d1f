#!/usr/bin/env python3
"""
Test script for the Instagram Service Provider Analysis API.

This script demonstrates how to interact with the API programmatically
and can be used for testing and validation.
"""

import requests
import json
import sys
import os
from pathlib import Path


def test_health_endpoint(base_url: str = "http://localhost:9005"):
    """Test the health check endpoint."""
    print("🔍 Testing health endpoint...")
    
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        response.raise_for_status()
        
        health_data = response.json()
        print(f"✅ Health check passed:")
        print(f"   Status: {health_data.get('status')}")
        print(f"   GPU Available: {health_data.get('gpu_available')}")
        print(f"   GPU Count: {health_data.get('gpu_count')}")
        print(f"   Model Loaded: {health_data.get('model_loaded')}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check failed: {e}")
        return False


def test_analyze_endpoint(
    image_path: str,
    instructions: str = "Analyze this Instagram profile for service provider information.",
    base_url: str = "http://localhost:9005"
):
    """Test the analyze endpoint with an image."""
    print(f"🔍 Testing analyze endpoint with image: {image_path}")
    
    # Check if image file exists
    if not os.path.exists(image_path):
        print(f"❌ Image file not found: {image_path}")
        return False
    
    try:
        # Prepare the request
        url = f"{base_url}/analyze"
        
        with open(image_path, 'rb') as image_file:
            files = {
                'image': (os.path.basename(image_path), image_file, 'image/png')
            }
            data = {
                'instructions': instructions
            }
            
            print("📤 Sending request to API...")
            response = requests.post(url, files=files, data=data, timeout=60)
            response.raise_for_status()
        
        # Process the response
        result = response.json()
        
        print("✅ Analysis completed successfully!")
        print("📊 Results:")
        print(json.dumps(result, indent=2))
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API request failed: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"   Response status: {e.response.status_code}")
            print(f"   Response body: {e.response.text}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse JSON response: {e}")
        return False


def create_test_image(output_path: str = "test_image.png"):
    """Create a simple test image if none is provided."""
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a simple test image
        img = Image.new('RGB', (1080, 1350), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add some text to simulate an Instagram profile
        try:
            # Try to use a default font
            font = ImageFont.load_default()
        except:
            font = None
        
        # Draw profile elements
        draw.rectangle([50, 50, 1030, 200], fill='lightgray')
        draw.text((60, 70), "@beautysalon_test", fill='black', font=font)
        draw.text((60, 100), "Beauty Salon & Spa", fill='black', font=font)
        draw.text((60, 130), "📍 Los Angeles, CA", fill='black', font=font)
        draw.text((60, 160), "📧 <EMAIL>", fill='black', font=font)
        
        draw.text((60, 220), "Services:", fill='black', font=font)
        draw.text((60, 250), "• Facials", fill='black', font=font)
        draw.text((60, 280), "• Massage Therapy", fill='black', font=font)
        draw.text((60, 310), "• Nail Services", fill='black', font=font)
        
        draw.text((60, 350), "Book online: beautysalon.com/book", fill='blue', font=font)
        
        img.save(output_path)
        print(f"✅ Created test image: {output_path}")
        return True
        
    except ImportError:
        print("❌ PIL not available for creating test image")
        return False
    except Exception as e:
        print(f"❌ Failed to create test image: {e}")
        return False


def main():
    """Main test function."""
    print("🚀 Instagram Service Provider Analysis API Test")
    print("=" * 50)
    
    # Parse command line arguments
    base_url = "http://localhost:9005"
    image_path = None
    
    if len(sys.argv) > 1:
        image_path = sys.argv[1]
    
    if len(sys.argv) > 2:
        base_url = sys.argv[2]
    
    # Test health endpoint first
    if not test_health_endpoint(base_url):
        print("\n❌ Health check failed. Make sure the API is running.")
        print(f"   Expected URL: {base_url}")
        print("   Start the API with: docker-compose up -d")
        return False
    
    print("\n" + "=" * 50)
    
    # Handle image path
    if not image_path:
        test_image_path = "test_image.png"
        print(f"📷 No image provided, creating test image: {test_image_path}")
        
        if create_test_image(test_image_path):
            image_path = test_image_path
        else:
            print("❌ Cannot create test image and no image provided.")
            print("Usage: python test_api.py [image_path] [base_url]")
            return False
    
    # Test analyze endpoint
    success = test_analyze_endpoint(
        image_path=image_path,
        instructions="Analyze this Instagram profile to determine if it's a service provider and extract business information.",
        base_url=base_url
    )
    
    print("\n" + "=" * 50)
    
    if success:
        print("🎉 All tests passed!")
        print(f"📖 API Documentation: {base_url}/docs")
    else:
        print("❌ Some tests failed.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
