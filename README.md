# Instagram Service Provider Analysis API

A production-grade FastAPI application that uses the DeepSeek-VL vision-language model to analyze Instagram profile screenshots and identify service providers, extracting key business information into structured JSON format.

## Features

- **GPU-Accelerated Inference**: Optimized for NVIDIA GPUs with CUDA support
- **4-bit Quantization**: Reduces VRAM usage while maintaining accuracy
- **Flash Attention 2**: Enhanced performance for transformer models
- **Containerized Deployment**: Docker-based deployment with multi-stage builds
- **Structured Output**: Reliable JSON extraction using few-shot prompting
- **Production Ready**: Comprehensive error handling, logging, and health checks

## Prerequisites

### Hardware Requirements
- NVIDIA GPU with at least 8GB VRAM (10GB+ recommended)
- CUDA-compatible GPU (Ampere, Ada, or Hopper architecture for Flash Attention 2)

### Software Requirements
- Docker with NVIDIA Container Toolkit
- NVIDIA GPU drivers (compatible with CUDA 12.1)

## Quick Start

### 1. Clone and Build

```bash
# Clone the repository
git clone <repository-url>
cd instagram_analyzer_api

# Build the Docker image
docker build -t instagram-analyzer .
```

### 2. Run with Docker

```bash
# Run the container with GPU access
docker run -d --gpus all -p 8000:8000 --name ig-analyzer instagram-analyzer
```

### 3. Run with Docker Compose (Recommended)

```bash
# Start the service
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the service
docker-compose down
```

## API Usage

### Health Check

```bash
curl http://localhost:8000/health
```

### Analyze Instagram Profile

**Using curl:**

```bash
curl -X POST "http://localhost:8000/analyze" \
  -H "accept: application/json" \
  -F "image=@profile_screenshot.png" \
  -F "instructions=Analyze this profile for beauty and wellness services."
```

**Using Python:**

```python
import requests
import json

# API endpoint
url = "http://localhost:8000/analyze"

# Prepare the request
files = {'image': ('profile.png', open('profile_screenshot.png', 'rb'), 'image/png')}
data = {'instructions': 'Analyze this profile for beauty services.'}

# Send request
response = requests.post(url, files=files, data=data)

# Process response
if response.status_code == 200:
    result = response.json()
    print(json.dumps(result, indent=2))
else:
    print(f"Error: {response.status_code} - {response.text}")
```

### Example Response

```json
{
  "username": "@beautystudio_la",
  "location": "Los Angeles, CA",
  "email": "<EMAIL>",
  "booking_link": "https://beautystudiola.com/book",
  "services_offered": ["Lash Extensions", "Microblading", "Brow Lamination"],
  "provider_type": "business"
}
```

## API Documentation

Once the service is running, visit:
- **Interactive API Docs**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc

## Project Structure

```
instagram_analyzer_api/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI application
│   ├── model_loader.py      # Model loading and management
│   └── schemas.py           # Pydantic models
├── Dockerfile               # Multi-stage Docker build
├── docker-compose.yml       # Docker Compose configuration
├── requirements.txt         # Python dependencies
├── .dockerignore           # Docker build exclusions
└── README.md               # This file
```

## Configuration

### Environment Variables

The application supports the following environment variables:

- `PYTHONUNBUFFERED=1`: Ensures Python output is not buffered
- `HUGGING_FACE_HUB_TOKEN`: Optional token for private model access

### Model Configuration

The application uses the `deepseek-ai/deepseek-vl-7b-chat` model with:
- 4-bit quantization (NF4) for memory efficiency
- Flash Attention 2 for performance optimization
- Automatic device mapping for multi-GPU setups

## Performance Optimization

### Memory Usage
- **Full Precision (FP16)**: ~15-16 GB VRAM
- **4-bit Quantization (NF4)**: ~5-6 GB VRAM

### Inference Speed
- **Cold Start**: ~10-15 seconds (first request)
- **Warm Inference**: ~2-5 seconds per request
- **Batch Processing**: Significantly faster for multiple images

## Troubleshooting

### Common Issues

1. **GPU Not Detected**
   ```bash
   # Verify NVIDIA Container Toolkit installation
   docker run --gpus all nvidia/cuda:12.1.1-base-ubuntu22.04 nvidia-smi
   ```

2. **Out of Memory Errors**
   - Ensure GPU has sufficient VRAM (8GB minimum)
   - Check for other processes using GPU memory
   - Consider using smaller batch sizes

3. **Model Loading Failures**
   - Verify internet connection for model download
   - Check available disk space (model is ~4GB)
   - Ensure Hugging Face Hub access

### Logs and Debugging

```bash
# View container logs
docker logs ig-analyzer

# Access container shell
docker exec -it ig-analyzer bash

# Check GPU status inside container
docker exec ig-analyzer nvidia-smi
```

## Development

### Local Development Setup

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Testing

```bash
# Test the health endpoint
curl http://localhost:8000/health

# Test with sample image
curl -X POST -F "image=@test_image.png" http://localhost:8000/analyze
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the API documentation at `/docs`
3. Open an issue on the repository
