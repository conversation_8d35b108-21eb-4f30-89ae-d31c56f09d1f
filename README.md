# Instagram Service Provider Analysis API

A production-grade FastAPI application that uses the DeepSeek-VL vision-language model via Ollama to analyze Instagram profile screenshots and identify service providers, extracting key business information into structured JSON format.

## Features

- **Ollama-Powered**: Uses Ollama for efficient model serving and management
- **GPU-Accelerated Inference**: Optimized for NVIDIA GPUs with CUDA support
- **Microservices Architecture**: Separate containers for API and model serving
- **Containerized Deployment**: Docker Compose orchestration with health checks
- **Structured Output**: Reliable JSON extraction using few-shot prompting
- **Production Ready**: Comprehensive error handling, logging, and health monitoring

## Prerequisites

### Hardware Requirements
- NVIDIA GPU with at least 8GB VRAM (10GB+ recommended)
- CUDA-compatible GPU for optimal performance

### Software Requirements
- Docker and Docker Compose
- NVIDIA Container Toolkit
- NVIDIA GPU drivers

## Quick Start

### 1. Clone and Build

```bash
# Clone the repository
git clone <repository-url>
cd instagram_analyzer_api

# Build the Docker image
docker build -t instagram-analyzer .
```

### 2. Run with Docker Compose (Recommended)

```bash
# Start both Ollama and API services
docker-compose up -d

# View logs for all services
docker-compose logs -f

# View logs for specific service
docker-compose logs -f instagram-analyzer-api
docker-compose logs -f ollama

# Stop all services
docker-compose down
```

### 3. Manual Docker Run (Alternative)

```bash
# Start Ollama container first
docker run -d --gpus all -p 9006:11434 --name ollama-deepseek \
  -v ollama_data:/root/.ollama ollama/ollama:latest

# Pull the DeepSeek-VL model
docker exec ollama-deepseek ollama pull deepseek-vl:7b

# Start the API container
docker run -d -p 9005:9005 --name ig-analyzer \
  -e OLLAMA_BASE_URL=http://ollama-deepseek:11434 \
  --link ollama-deepseek instagram-analyzer
```

## API Usage

### Health Check

```bash
curl http://localhost:9005/health
```

### Analyze Instagram Profile

**Using curl:**

```bash
curl -X POST "http://localhost:9005/analyze" \
  -H "accept: application/json" \
  -F "image=@profile_screenshot.png" \
  -F "instructions=Analyze this profile for beauty and wellness services."
```

**Using Python:**

```python
import requests
import json

# API endpoint
url = "http://localhost:9005/analyze"

# Prepare the request
files = {'image': ('profile.png', open('profile_screenshot.png', 'rb'), 'image/png')}
data = {'instructions': 'Analyze this profile for beauty services.'}

# Send request
response = requests.post(url, files=files, data=data)

# Process response
if response.status_code == 200:
    result = response.json()
    print(json.dumps(result, indent=2))
else:
    print(f"Error: {response.status_code} - {response.text}")
```

### Example Response

```json
{
  "username": "@beautystudio_la",
  "location": "Los Angeles, CA",
  "email": "<EMAIL>",
  "booking_link": "https://beautystudiola.com/book",
  "services_offered": ["Lash Extensions", "Microblading", "Brow Lamination"],
  "provider_type": "business"
}
```

## API Documentation

Once the service is running, visit:
- **Interactive API Docs**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc

## Project Structure

```
instagram_analyzer_api/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI application
│   ├── model_loader.py      # Ollama client management
│   └── schemas.py           # Pydantic models
├── Dockerfile               # Lightweight API container
├── docker-compose.yml       # Multi-service orchestration
├── requirements.txt         # Python dependencies
├── .dockerignore           # Docker build exclusions
├── test_api.py             # API testing script
└── README.md               # This file
```

## Architecture

The application uses a microservices architecture with two main components:

1. **Ollama Service** (Port 9006): Runs the DeepSeek-VL model
2. **FastAPI Service** (Port 9005): Provides the REST API interface

```
┌─────────────────┐    HTTP     ┌─────────────────┐
│   FastAPI API   │ ──────────► │  Ollama Service │
│   (Port 9005)   │             │   (Port 9006)   │
└─────────────────┘             └─────────────────┘
        │                               │
        │                               │
    REST API                    DeepSeek-VL Model
   (External)                      (GPU-Accelerated)
```

## Configuration

### Environment Variables

The application supports the following environment variables:

- `PYTHONUNBUFFERED=1`: Ensures Python output is not buffered
- `OLLAMA_BASE_URL`: URL for the Ollama service (default: http://ollama:11434)
- `OLLAMA_HOST`: Host binding for Ollama service (default: 0.0.0.0)

### Model Configuration

The application uses the `deepseek-vl:7b` model via Ollama with:
- Automatic GPU acceleration when available
- Efficient model serving and caching
- Built-in model management and updates

## Performance Optimization

### Memory Usage
- **Ollama Optimized**: ~6-8 GB VRAM (automatically optimized)
- **Model Caching**: Efficient memory management
- **Concurrent Requests**: Handled via Ollama's built-in queuing

### Inference Speed
- **Cold Start**: ~30-60 seconds (model download + initialization)
- **Warm Inference**: ~3-8 seconds per request
- **Concurrent Processing**: Multiple requests handled efficiently

## Troubleshooting

### Common Issues

1. **Ollama Service Not Starting**
   ```bash
   # Check Ollama container logs
   docker-compose logs ollama

   # Verify GPU access
   docker run --gpus all nvidia/cuda:12.1.1-base-ubuntu22.04 nvidia-smi
   ```

2. **Model Download Issues**
   ```bash
   # Manually pull the model
   docker exec ollama-deepseek-vl ollama pull deepseek-vl:7b

   # Check available models
   docker exec ollama-deepseek-vl ollama list
   ```

3. **API Connection Errors**
   ```bash
   # Check if Ollama is accessible from API container
   docker exec instagram_analyzer_api-instagram-analyzer-api-1 curl http://ollama:11434/api/tags
   ```

4. **Out of Memory Errors**
   - Ensure GPU has sufficient VRAM (8GB minimum)
   - Check for other processes using GPU memory
   - Restart Ollama service if needed

### Logs and Debugging

```bash
# View container logs
docker logs ig-analyzer

# Access container shell
docker exec -it ig-analyzer bash

# Check GPU status inside container
docker exec ig-analyzer nvidia-smi
```

## Development

### Local Development Setup

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Testing

```bash
# Test the health endpoint
curl http://localhost:8000/health

# Test with sample image
curl -X POST -F "image=@test_image.png" http://localhost:8000/analyze
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the API documentation at `/docs`
3. Open an issue on the repository
