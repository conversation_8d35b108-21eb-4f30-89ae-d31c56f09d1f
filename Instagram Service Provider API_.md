

# **Building a Production-Grade Vision-Language API: A Deep Dive into Deploying DeepSeek-VL with FastAPI and Docker**

## **Architecting the Foundation: Host and Project Setup**

The successful deployment of a high-performance, GPU-accelerated machine learning service begins not with code, but with a meticulously prepared environment and a well-conceived project structure. This foundational section details the necessary steps to configure the host machine for GPU-enabled containerization and establishes a professional, scalable layout for the FastAPI application. Adhering to these principles of separated concerns and clear dependency management is paramount for long-term maintainability and scalability.

### **Preparing the Host: NVIDIA Driver and Container Toolkit Installation**

To enable Docker containers to harness the computational power of NVIDIA GPUs, the host system requires specific low-level drivers and a specialized toolkit that bridges the gap between the hardware and the containerization runtime.

The first and most critical prerequisite is the installation of a compatible NVIDIA driver on the host machine. This driver interacts directly with the physical GPU hardware. Its presence and functionality can be verified by executing the nvidia-smi command in the host's terminal, which should output a detailed summary of the GPU's status, driver version, and CUDA version.1 It is essential to understand that these hardware-specific drivers reside

*only* on the host system and should not be installed within the Docker image itself.2 This separation is a core principle of portable GPU containerization; the container relies on the host for hardware abstraction, not for the drivers themselves.

With the driver in place, the next step is to install the NVIDIA Container Toolkit. This toolkit is a collection of libraries and command-line utilities that integrate with the Docker daemon to automatically configure containers for GPU support.2 It acts as a runtime hook, exposing the host's GPU capabilities to the containers in a standardized way.

For a common Linux distribution such as Ubuntu, the installation process involves adding NVIDIA's package repository and its corresponding GPG key to the system's package manager. This ensures the authenticity and integrity of the software being installed.1 The typical commands are as follows:

1. **Add the GPG Key and Repository:**  
   Bash  
   distribution=$(. /etc/os-release;echo $ID$VERSION\_ID) \\  
      && curl \-s \-L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add \- \\  
      && curl \-s \-L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

2. **Install the Toolkit:**  
   Bash  
   sudo apt-get update  
   sudo apt-get install \-y nvidia-container-toolkit

3. **Restart the Docker Daemon:**  
   Bash  
   sudo systemctl restart docker

After installation, it is crucial to verify that the entire stack is functioning correctly. This can be accomplished by running a test container using an official NVIDIA CUDA image. The command docker run \--gpus all nvidia/cuda:12.1.1-base-ubuntu22.04 nvidia-smi should execute the nvidia-smi command *inside* the container and produce output nearly identical to running it on the host.1 A successful execution of this command provides definitive confirmation that the host is ready to run GPU-accelerated workloads in Docker.

### **Blueprint for Scalability: Structuring the FastAPI Project**

A logical and scalable directory structure is not a premature optimization; it is a prerequisite for a maintainable application. For this project, a "module-functionality" structure is recommended over a simpler "file-type" structure. This approach groups files related to a specific feature or domain together, which enhances clarity and scales more effectively as new functionalities are added.6

The proposed project structure, based on established best practices 6, is as follows:

instagram\_analyzer\_api/  
├── app/  
│   ├── \_\_init\_\_.py  
│   ├── main.py             \# FastAPI app initialization and router inclusion  
│   ├── core/  
│   │   ├── \_\_init\_\_.py  
│   │   └── config.py       \# Pydantic settings, model paths, env vars  
│   ├── api/  
│   │   ├── \_\_init\_\_.py  
│   │   ├── router.py       \# Defines the main API endpoint (/api/analyze)  
│   │   └── schemas.py      \# Pydantic models for request/response validation  
│   └── services/  
│       ├── \_\_init\_\_.py  
│       └── analysis\_service.py \# Core logic for model loading and inference  
├── tests/  
│   └── test\_api.py         \# Test suite for the API endpoint  
├──.env                    \# Environment variables  
├──.dockerignore           \# Files/directories to exclude from Docker build  
├── Dockerfile              \# Instructions for building the container image  
└── requirements.txt        \# Python package dependencies

The role of each component is clearly defined by this structure:

* **app/main.py**: The application's entry point. It initializes the FastAPI instance and includes the API routers.  
* **app/core/config.py**: Manages all configuration, such as model paths or API keys, often loaded from environment variables.  
* **app/api/router.py**: Contains the API endpoint logic (handling HTTP requests and responses). It remains lean by delegating business logic.  
* **app/api/schemas.py**: Defines the Pydantic models that validate incoming data and structure outgoing responses, serving as the API's data contract.  
* **app/services/analysis\_service.py**: The heart of the application's "business logic." This module will encapsulate all interactions with the DeepSeek-VL model, including loading, preprocessing, and inference. This separation of concerns ensures that the model logic is decoupled from the web framework's routing logic.7

### **Defining Dependencies for a GPU-Powered Environment**

The requirements.txt file lists all the Python packages necessary for the project to run. Each dependency plays a specific role in the application stack.

* fastapi: The high-performance web framework for building the API.9  
* uvicorn\[standard\]: The lightning-fast ASGI server that runs the FastAPI application, with the \[standard\] option providing recommended performance extras.9  
* python-multipart: A crucial dependency that enables FastAPI to parse multipart/form-data, which is the encoding used for file uploads.12  
* torch, torchvision, torchaudio: The core PyTorch deep learning library and its companion packages. The version should be selected to match the CUDA version of the base Docker image.1  
* transformers: The Hugging Face library used to download and interact with the DeepSeek-VL model.14  
* deepseek-vl: The specific package required by the deepseek-vl-7b-chat model for its custom code and processor.14  
* bitsandbytes: The library that enables model quantization (e.g., to 4-bit or 8-bit precision) to reduce VRAM usage.15  
* accelerate: A Hugging Face library that simplifies running PyTorch models across different hardware configurations and is often a prerequisite for advanced features.  
* flash-attn: The package for Flash Attention 2, an optimized attention mechanism for faster inference on compatible GPUs.16  
* pillow: The Python Imaging Library (PIL) fork, used for all image preprocessing tasks like opening, resizing, and format conversion.18  
* python-dotenv: A utility for loading environment variables from a .env file into the application's environment, which is a best practice for managing configuration.7  
* requests: A standard library for making HTTP requests, which will be used in the client-side script to test the final API.19

## **The Vessel: Crafting an Optimized Docker Image for GPU Inference**

Containerization is the key to creating a portable and reproducible deployment environment. For a complex, GPU-dependent application, crafting an efficient Docker image is a critical task. The primary objective is to produce a final image that is as lean as possible, containing only the necessary runtime dependencies, thereby reducing storage footprint, improving security posture, and speeding up deployment times.

### **The Multi-Stage Dockerfile: From Build Environment to Lean Production Image**

A naive, single-stage Dockerfile for a PyTorch project often results in an excessively large image. This is because it bundles build-time dependencies, such as compilers and development headers, along with the final application code.21 The professional standard to circumvent this issue is a multi-stage build. This approach uses an intermediate build container to compile dependencies and then copies only the necessary artifacts into a clean, lightweight final container.

The following multi-stage Dockerfile is designed for this purpose:

Stage 1: The builder Stage  
This stage acts as a comprehensive build environment. It starts with a devel image from NVIDIA, which includes the full CUDA toolkit, compilers (gcc, g++), and other tools required to build Python packages from source, such as flash-attn.

Dockerfile

\# Stage 1: Build the environment with all dependencies  
FROM nvidia/cuda:12.1.1\-devel-ubuntu22.04 AS builder

\# Prevent interactive prompts during installation  
ENV DEBIAN\_FRONTEND=noninteractive  
ENV PYTHONUNBUFFERED=1

\# Install system dependencies and Python  
RUN apt-get update && \\  
    apt-get install \-y \--no-install-recommends \\  
    python3.10 \\  
    python3.10-venv \\  
    python3-pip \\  
    build-essential \\  
    git && \\  
    rm \-rf /var/lib/apt/lists/\*

\# Create and activate a virtual environment  
RUN python3.10 \-m venv /opt/venv  
ENV PATH="/opt/venv/bin:$PATH"

\# Upgrade pip and install wheel  
RUN python \-m pip install \--upgrade pip wheel

\# Copy requirements and install packages  
\# This caches the layer with installed packages  
COPY requirements.txt.  
RUN python \-m pip install \--no-cache-dir \-r requirements.txt

Stage 2: The final Stage  
This stage builds the lean production image. It starts from a base CUDA image, which is significantly smaller than the devel image because it contains only the CUDA runtime libraries, not the development tools.

Dockerfile

\# Stage 2: Create the final, lean production image  
FROM nvidia/cuda:12.1.1\-base-ubuntu22.04

\# Set non-interactive frontend  
ENV DEBIAN\_FRONTEND=noninteractive  
ENV PYTHONUNBUFFERED=1

\# Create a non-root user for security  
RUN useradd \--create-home appuser  
WORKDIR /home/<USER>

\# Copy the virtual environment from the builder stage  
COPY \--from=builder /opt/venv /opt/venv

\# Copy the application code  
COPY \--chown=appuser:appuser./app./app

\# Set the PATH to use the venv  
ENV PATH="/opt/venv/bin:$PATH"

\# Switch to the non-root user  
USER appuser

\# Expose the port the app runs on  
EXPOSE 8000

\# Define the command to run the application  
CMD \["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"\]

To complement the Dockerfile, a .dockerignore file is essential. This file instructs the Docker daemon to exclude specified files and directories from the build context sent to the daemon. This prevents unnecessary files, such as local virtual environments, Git history, and Python bytecode cache, from being copied into the image, which speeds up the build process and avoids potential conflicts.21

A typical .dockerignore file would include:

\# Git  
.git  
.gitignore

\# Docker  
Dockerfile

\# Python  
\_\_pycache\_\_/  
\*.pyc  
\*.pyo  
\*.pyd  
venv/  
.venv/

\# Environment  
.env

### **Building the Image and Verifying GPU Passthrough**

With the Dockerfile and .dockerignore file in place, the image can be built using a simple command from the project's root directory:  
docker build \-t instagram\_analyzer\_api..1  
Once the build is complete, the container can be launched with the following command, which enables GPU access and maps the container's port to the host:  
docker run \--rm \-it \--gpus all \-p 8000:8000 instagram\_analyzer\_api.  
A breakdown of the flags is as follows:

* \--rm: Automatically removes the container when it exits, which is useful for cleanup during development.  
* \-it: Runs the container in interactive mode and attaches a terminal.  
* \--gpus all: This is the critical flag that leverages the NVIDIA Container Toolkit to grant the container access to all available GPUs on the host.1  
* \-p 8000:8000: Maps port 8000 on the host to port 8000 inside the container, allowing external access to the API.

Upon startup, the container logs should show the Uvicorn server starting up. To explicitly verify GPU access from within the application, a temporary health check endpoint can be added to app/main.py. This endpoint, as suggested in 9, can call

torch.cuda.is\_available() and return the boolean result, providing a definitive, programmatic confirmation that the application can see and use the GPU.

## **The Core Engine: Implementing the DeepSeek-VL Analysis Service**

This section details the implementation of the application's core machine learning component. This involves loading and optimizing the deepseek-vl-7b-chat model, establishing a robust pipeline for preprocessing incoming images, and, most critically, engineering a sophisticated prompt to guide the model toward producing the desired structured JSON output. This service will be encapsulated within the app/services/analysis\_service.py module.

### **The Model Service: Loading, Quantizing, and Optimizing DeepSeek-VL**

To ensure efficiency and responsiveness, the large vision-language model must be handled with care. The primary goals are to load the model into VRAM only once at application startup and to apply optimizations that reduce its memory footprint and accelerate its inference speed.

**Singleton Model Loading:** The model, which can be several gigabytes in size, should be treated as a singleton resource. Loading it on every API request would be prohibitively slow and would quickly exhaust GPU memory. A common pattern is to initialize the model and its associated processor as module-level global variables, ensuring they are created only when the Python module is first imported at application startup.

**Model Loading and Optimization:** The deepseek-vl-7b-chat model can be loaded using the Hugging Face transformers library. The implementation will integrate several key optimizations:

1. **4-bit Quantization:** To make a 7-billion-parameter model feasible for local deployment on consumer-grade GPUs, its memory footprint must be drastically reduced. This is achieved through quantization, a process that represents the model's weights with lower-precision numbers. Using the bitsandbytes library, the model can be loaded in 4-bit precision, reducing its VRAM requirement from over 14 GB to approximately 5-6 GB. This is configured via a BitsAndBytesConfig object passed to the from\_pretrained method.15 The configuration  
   bnb\_4bit\_quant\_type="nf4" specifies the "Normal Float 4" data type, which is optimized for weights from a normal distribution, and bnb\_4bit\_compute\_dtype=torch.bfloat16 sets the data type for computation, which can speed up operations on compatible hardware (Ampere series or newer).  
2. **Flash Attention 2:** This is an advanced algorithm that reorders the self-attention computation to be more efficient by minimizing slow read/write operations to the GPU's high-bandwidth memory (HBM).16 It is enabled by adding the argument  
   attn\_implementation="flash\_attention\_2" to the from\_pretrained call. This optimization requires specific hardware (NVIDIA Ampere, Ada, or Hopper GPUs) and that the model be loaded in fp16 or bf16 data types.16 While its performance benefits are most pronounced for tasks with long input sequences, it is a best practice to enable it for potential future use cases like batch processing.23  
3. **Model Warm-up:** The first inference call to a model after loading can be significantly slower than subsequent calls due to the one-time cost of CUDA kernel initialization. To mitigate this "cold start" latency, a warm-up procedure is implemented. Immediately after the model is loaded into the GPU, a single dummy inference is performed using a random tensor. This pre-compiles and caches the necessary CUDA kernels, ensuring that the first real API request is served with optimal latency.9

### **The Preprocessing Pipeline: Handling and Preparing Images with Pillow**

The deepseek-vl-7b-chat model has specific requirements for its image inputs: they must be in a 1024x1024 pixel resolution.14 The preprocessing function will take the raw

UploadFile object from the FastAPI endpoint and prepare it accordingly using the Pillow library. This process avoids writing the file to disk, operating entirely in memory for maximum efficiency.

The steps in the preprocessing pipeline are:

1. Read the uploaded file's content as bytes from the in-memory buffer: image\_bytes \= await uploaded\_file.read().  
2. Open the image from this byte stream using Pillow, which can handle in-memory file-like objects: image \= Image.open(io.BytesIO(image\_bytes)).18  
3. Ensure color consistency by converting the image to the 'RGB' mode: image \= image.convert("RGB"). This step is crucial as it standardizes the input, removing any alpha (transparency) channel from formats like PNG and ensuring a consistent 3-channel input for the model.18  
4. Resize the image to the model's required 1024x1024 dimensions: image \= image.resize((1024, 1024)).18  
5. The function returns the fully processed Pillow Image object, which is now ready to be passed to the model's VLChatProcessor.

### **The Brains of the Operation: Advanced Prompt Engineering for Structured JSON Extraction**

Guiding a vision-language model to consistently produce a valid, structured JSON output is one of the most significant challenges in building this API. The reliability of the entire service hinges on the effectiveness of the prompt. There is a clear spectrum of prompting techniques, each offering a different balance between implementation simplicity and output robustness.

* **Strategy 1: Zero-Shot Instruction.** This is the simplest approach, involving a direct command within the prompt, such as "Analyze the provided Instagram profile screenshot and output the requested information in a valid JSON format".26 While easy to implement, this method is often brittle. The model may generate conversational text before or after the JSON object, or it may produce malformed JSON, leading to parsing errors downstream.  
* **Strategy 2: Few-Shot Examples (Recommended).** This technique dramatically improves reliability by leveraging the model's in-context learning capabilities.28 The prompt is structured to include one or more complete examples of the task. Each example consists of a sample input (e.g., a description of a sample image) and the exact, perfectly formatted JSON output expected for that input. By seeing these examples, the model learns the precise structure, data types, and key names required for the output. This is the recommended strategy for this project as it offers a strong balance of high reliability without the complexity of external enforcement mechanisms.28  
* **Strategy 3: Schema-in-Prompt.** A more explicit form of guidance involves embedding a representation of the desired JSON schema directly into the prompt text.30 This could be a textual description of the Pydantic model or the JSON Schema definition itself. The model is then instructed to generate a JSON object that strictly adheres to this provided schema. This can be more robust than a simple zero-shot instruction, especially for complex or nested JSON structures.

The following table summarizes the trade-offs between these strategies, providing a clear guide for selecting the appropriate technique based on project requirements.

### **Table 1: Prompting Strategy Trade-offs**

| Strategy | Implementation Complexity | Output Reliability | Prompt Cost (Tokens) | Best For |
| :---- | :---- | :---- | :---- | :---- |
| **Zero-Shot Instruction** | Low | Low to Medium | Low | Quick prototyping, simple extractions. |
| **Few-Shot Examples** | Medium | High | Medium to High | Production-grade applications, complex extractions. |
| **Schema-in-Prompt** | Medium | High | High | Applications requiring strict validation and complex nested structures. |
| **Schema-Enforced (vLLM)** | High | Very High | Low (prompt) | High-throughput services where absolute reliability is paramount. |

For this implementation, the Few-Shot strategy will be used to build the final prompt, ensuring a high likelihood of receiving well-formed, parseable JSON from the DeepSeek-VL model.

## **The Public Interface: Building the FastAPI Endpoints**

With the core analysis engine defined, the next step is to expose its functionality through a web interface. This section details the creation of the FastAPI endpoints, focusing on robust data handling, seamless integration with the analysis service, and the use of Pydantic schemas to enforce a strict data contract for both inputs and outputs.

### **Designing the API Route for Image and Instruction Ingestion**

The API must be capable of receiving both an image file and textual instructions within a single HTTP request. The standard and most efficient way to achieve this is by using the multipart/form-data content type. This encoding allows binary file data and regular form fields to be transmitted together.11

The endpoint will be a POST route, implemented in app/api/router.py. The function signature is designed to explicitly tell FastAPI how to parse the incoming multipart data:

Python

from fastapi import APIRouter, File, Form, UploadFile  
from..api import schemas

router \= APIRouter()

@router.post(  
    "/analyze",  
    response\_model=schemas.InstagramProfileAnalysis,  
    summary="Analyze Instagram Profile Screenshot"  
)  
async def analyze\_profile(  
    image: UploadFile \= File(..., description="The Instagram profile screenshot to analyze."),  
    instructions: str \= Form(..., description="The prompt or instructions for the analysis.")  
):  
    \#... implementation follows...

In this signature:

* image: UploadFile \= File(...): This declares a required form field named image. FastAPI will interpret this as a file upload and provide it as an UploadFile object. Using UploadFile is the recommended practice as it handles large files efficiently by spooling them to disk if they exceed a certain size in memory, thus preventing memory exhaustion.12  
* instructions: str \= Form(...): This declares a required form field named instructions. The Form() dependency explicitly tells FastAPI to extract this value from the form data fields, rather than from a JSON request body.10

### **Integrating the Analysis Service: The End-to-End Request-Response Flow**

The API route function serves as the controller that orchestrates the entire analysis process. It connects the incoming web request to the backend machine learning service and handles the final response.

The end-to-end flow within the analyze\_profile function is as follows:

1. **Receive Inputs:** FastAPI automatically parses the request and provides the image (UploadFile) and instructions (str).  
2. **Delegate to Service:** The function will then call the core analysis function located in the analysis\_service.py module. This call passes the received image and instructions to the service layer.  
3. **Await Response:** The analysis service will perform the necessary image preprocessing and model inference, which is a computationally intensive and synchronous operation. The API route will await the result.  
4. **Parse and Validate:** The analysis service returns the model's output, which is expected to be a JSON string. The route function is responsible for parsing this string into a Python dictionary. A try-except block is essential here to gracefully handle cases where the VLM fails to produce valid JSON, returning an appropriate HTTP error (e.g., 422 Unprocessable Entity or 500 Internal Server Error) instead of crashing.  
5. **Return Structured Response:** The parsed dictionary is then returned. FastAPI, guided by the response\_model defined in the decorator, will automatically validate this dictionary against the Pydantic schema and serialize it into a JSON response for the client.

### **Pydantic Schemas for Robust Input Validation and Structured Output**

Pydantic schemas are a cornerstone of FastAPI's power, providing data validation, serialization, and automatic documentation generation.7 For this API, a Pydantic model will be defined in

app/api/schemas.py to represent the structured output.

Python

from pydantic import BaseModel, EmailStr, HttpUrl  
from typing import List, Optional, Literal

class InstagramProfileAnalysis(BaseModel):  
    username: str  
    location: Optional\[str\] \= None  
    email: Optional \= None  
    booking\_link: Optional\[HttpUrl\] \= None  
    services\_offered: List\[str\]  
    provider\_type: Literal\["individual", "business"\]

    class Config:  
        from\_attributes \= True

This InstagramProfileAnalysis schema serves two critical functions:

1. **Data Contract Definition:** It clearly defines the expected structure of the API's successful response. This includes field names, data types (e.g., str, List\[str\]), and even more specific validation like EmailStr for email addresses and HttpUrl for booking links. The Optional type indicates fields that may not always be present in the source image.  
2. **Response Validation and Documentation:** By setting response\_model=InstagramProfileAnalysis in the endpoint decorator, FastAPI performs a final, crucial step before sending the response: it validates that the data being returned from the function conforms to this schema. If there is a mismatch (e.g., a missing required field or an incorrect data type), FastAPI will raise a server-side error. This acts as a powerful guarantee of the API's output contract. Furthermore, this schema is used to automatically generate rich, interactive API documentation (in the OpenAPI/Swagger UI), showing clients exactly what to expect from a successful request.

## **Performance Tuning and Advanced Optimization**

Achieving an "optimized approach" for a local deep learning API involves more than just functional code. It requires a deliberate focus on performance, particularly concerning GPU memory (VRAM) usage and inference latency. This section provides a comparative analysis of optimization strategies and offers an outlook on more advanced serving architectures for future scaling.

### **A Comparative Analysis of Quantization Strategies**

The choice of numerical precision for the model's weights is the single most impactful decision for managing VRAM consumption. A 7-billion-parameter model like deepseek-vl-7b-chat stored in its native 16-bit floating-point format (FP16) requires a substantial amount of GPU memory, often exceeding the capacity of many consumer-grade cards. Quantization addresses this by representing the weights using fewer bits, with a trade-off between memory savings and potential accuracy degradation.15

The following table provides a concrete comparison of the different precision levels, enabling an informed decision based on available hardware. The values are estimates for the deepseek-vl-7b-chat model.

### **Table 2: Quantization Performance and Resource Impact (deepseek-vl-7b-chat)**

| Precision | Model Size (Disk) | VRAM Usage (Inference) | Relative Speed | Notes |
| :---- | :---- | :---- | :---- | :---- |
| **FP16 (Full)** | \~14 GB | \~15-16 GB | 1x (Baseline) | Highest accuracy, requires high-end GPU. |
| **INT8 (8-bit)** | \~7 GB | \~8-9 GB | \~0.9x \- 1.1x | Good balance, minimal accuracy loss. Viable for some high-end consumer GPUs. |
| **NF4 (4-bit)** | \~4 GB | \~5-6 GB | \~0.8x \- 1.0x | Most memory-efficient, slight accuracy loss possible. Recommended for most consumer GPUs. |

As the table illustrates, 4-bit quantization using the Normal Float 4 (NF4) data type reduces the VRAM footprint by nearly 66% compared to the full FP16 model, making it a viable option for GPUs with as little as 8 GB of VRAM.32 This makes it the recommended approach for this project's local deployment context.

### **Maximizing Throughput with Flash Attention 2**

Flash Attention 2 is a highly optimized implementation of the attention mechanism, which is the most computationally intensive part of a transformer model. Its primary innovation is the fusion of multiple steps of the attention calculation into a single GPU kernel. This drastically reduces the number of slow data transfers between the GPU's ultra-fast on-chip SRAM and its larger, but slower, High-Bandwidth Memory (HBM).16

While the most significant speedups from Flash Attention 2 are observed during the training of models on very long sequences of text, it can still provide benefits for inference.16 However, it is important to set realistic expectations. For single-image inference with relatively short prompts, the performance gain may be marginal or even negligible.23 Nonetheless, enabling it via the

attn\_implementation="flash\_attention\_2" flag is a recommended best practice, as it positions the API for future enhancements like batch processing, where its benefits would become more apparent.

### **An Outlook on High-Concurrency Serving with vLLM**

The architecture presented in this report—FastAPI with a model loaded via the transformers library—is robust and perfectly suited for a local API, development, or low-to-moderate traffic scenarios. However, for a production system designed to handle high concurrency and maximize GPU utilization, a dedicated inference serving engine like vLLM presents a clear and powerful upgrade path.

vLLM is a high-throughput serving library specifically designed for LLMs. It introduces several key optimizations not present in a standard transformers implementation, most notably **PagedAttention**. This technique manages the GPU memory for attention keys and values more efficiently, akin to how virtual memory and paging work in an operating system. This allows vLLM to achieve much higher batch sizes, significantly increasing GPU throughput and reducing memory fragmentation.33

Furthermore, vLLM offers first-class support for **structured outputs**. It can constrain the model's generation process at the token level to ensure the output strictly conforms to a provided JSON schema or regular expression. This guided\_json feature is the most reliable method for producing structured data, eliminating the possibility of malformed outputs and the need for downstream parsing-and-validation logic.34 Transitioning to vLLM would be the logical next step when scaling the service from a local tool to a high-performance, production-grade system.

## **Deployment, Testing, and Usage**

This final section provides the complete, runnable application codebase, instructions for launching the service using Docker Compose, and practical examples for interacting with the API from both the command line and a Python script.

### **The Complete Application Codebase**

The following are the complete contents of each file in the project structure. This allows for a direct copy-paste setup.

**requirements.txt**

fastapi  
uvicorn\[standard\]  
python-multipart  
torch  
torchvision  
torchaudio  
transformers  
deepseek-vl  
bitsandbytes  
accelerate  
flash-attn \--no-build-isolation  
pillow  
python-dotenv  
requests

**.env**

\# This file can be used to store configuration variables  
\# For now, we don't have any, but it's good practice to have it.  
\# Example: HUGGING\_FACE\_HUB\_TOKEN=your\_token\_here

**app/core/config.py**

Python

import os  
from dotenv import load\_dotenv

load\_dotenv()

\# Central place for configuration  
MODEL\_PATH \= "deepseek-ai/deepseek-vl-7b-chat"

**app/api/schemas.py**

Python

from pydantic import BaseModel, EmailStr, HttpUrl  
from typing import List, Optional, Literal

class InstagramProfileAnalysis(BaseModel):  
    username: str  
    location: Optional\[str\] \= None  
    email: Optional \= None  
    booking\_link: Optional\[HttpUrl\] \= None  
    services\_offered: List\[str\]  
    provider\_type: Literal\["individual", "business"\]

    class Config:  
        from\_attributes \= True  
        json\_schema\_extra \= {  
            "example": {  
                "username": "@beautystudio\_la",  
                "location": "Los Angeles, CA",  
                "email": "<EMAIL>",  
                "booking\_link": "https://beautystudiola.com/book",  
                "services\_offered":,  
                "provider\_type": "business"  
            }  
        }

**app/services/analysis\_service.py**

Python

import torch  
from transformers import AutoModelForCausalLM, BitsAndBytesConfig  
from deepseek\_vl.models import VLChatProcessor, MultiModalityCausalLM  
from deepseek\_vl.utils.io import load\_pil\_images  
from PIL import Image  
import io  
from typing import List

from..core import config

class AnalysisService:  
    \_instance \= None  
      
    def \_\_new\_\_(cls):  
        if cls.\_instance is None:  
            cls.\_instance \= super(AnalysisService, cls).\_\_new\_\_(cls)  
            cls.\_instance.\_initialized \= False  
        return cls.\_instance

    def \_\_init\_\_(self):  
        if self.\_initialized:  
            return  
          
        print("Initializing Analysis Service and loading model...")  
          
        quantization\_config \= BitsAndBytesConfig(  
            load\_in\_4bit=True,  
            bnb\_4bit\_quant\_type="nf4",  
            bnb\_4bit\_compute\_dtype=torch.bfloat16,  
        )

        self.vl\_chat\_processor: VLChatProcessor \= VLChatProcessor.from\_pretrained(config.MODEL\_PATH)  
        self.tokenizer \= self.vl\_chat\_processor.tokenizer

        self.model: MultiModalityCausalLM \= AutoModelForCausalLM.from\_pretrained(  
            config.MODEL\_PATH,  
            trust\_remote\_code=True,  
            quantization\_config=quantization\_config,  
            attn\_implementation="flash\_attention\_2",  
        )  
        \# Note:.cuda() is handled by accelerate with quantization\_config  
        self.model.eval()

        \# Warm-up the model  
        self.\_warmup\_model()  
          
        self.\_initialized \= True  
        print("Model loaded and warmed up successfully.")

    def \_warmup\_model(self):  
        print("Warming up the model...")  
        try:  
            dummy\_image \= Image.new('RGB', (1024, 1024), color \= 'red')  
            dummy\_conversation \= \[{"role": "User", "content": "\<image\_placeholder\>test", "images": \["dummy"\]}\]  
              
            with torch.no\_grad():  
                prepare\_inputs \= self.vl\_chat\_processor(  
                    conversations=dummy\_conversation,  
                    images=\[dummy\_image\],  
                    force\_batchify=True  
                ).to(self.model.device)  
                  
                inputs\_embeds \= self.model.prepare\_inputs\_embeds(\*\*prepare\_inputs)

                self.model.language\_model.generate(  
                    inputs\_embeds=inputs\_embeds,  
                    attention\_mask=prepare\_inputs.attention\_mask,  
                    pad\_token\_id=self.tokenizer.eos\_token\_id,  
                    max\_new\_tokens=5,  
                )  
            print("Warm-up complete.")  
        except Exception as e:  
            print(f"An error occurred during model warm-up: {e}")

    def \_preprocess\_image(self, image\_bytes: bytes) \-\> Image.Image:  
        image \= Image.open(io.BytesIO(image\_bytes))  
        if image.mode\!= "RGB":  
            image \= image.convert("RGB")  
        processed\_image \= image.resize((1024, 1024))  
        return processed\_image

    def \_construct\_prompt(self, instructions: str) \-\> List\[dict\]:  
        \# Few-Shot Prompting Strategy  
        prompt \= f"""  
You are an expert analyst tasked with extracting structured information from Instagram profile screenshots.  
Your goal is to identify if the account is a service provider and extract key details.  
Analyze the user's bio and any visible posts in the provided image.

\*\*Example 1:\*\*  
\*Input Image shows a profile for a lash artist.\*  
\*\*Output JSON:\*\*  
\`\`\`json  
{{  
    "username": "@lashbylily",  
    "location": "Miami, FL",  
    "email": "<EMAIL>",  
    "booking\_link": "lashbylily.booksy.com",  
    "services\_offered": \["Classic Lashes", "Volume Lashes", "Hybrid Lashes"\],  
    "provider\_type": "individual"  
}}

Example 2:  
Input Image shows a profile for a generic travel blogger.  
Output JSON:

JSON

{{  
    "username": "@traveling\_tom",  
    "location": null,  
    "email": null,  
    "booking\_link": null,  
    "services\_offered":,  
    "provider\_type": "individual"  
}}

Based on these examples, analyze the following image and instructions.  
Provide your response as a valid JSON object only, with no additional text or explanations.  
Instructions: {instructions}  
"""  
return \[{"role": "User", "content": f"

def run\_analysis(self, image\_bytes: bytes, instructions: str) \-\> str:  
    processed\_image \= self.\_preprocess\_image(image\_bytes)  
    conversation \= self.\_construct\_prompt(instructions)  
      
    pil\_images \= \[processed\_image\]  
      
    with torch.no\_grad():  
        prepare\_inputs \= self.vl\_chat\_processor(  
            conversations=conversation,  
            images=pil\_images,  
            force\_batchify=True  
        ).to(self.model.device)  
          
        inputs\_embeds \= self.model.prepare\_inputs\_embeds(\*\*prepare\_inputs)

        outputs \= self.model.language\_model.generate(  
            inputs\_embeds=inputs\_embeds,  
            attention\_mask=prepare\_inputs.attention\_mask,  
            pad\_token\_id=self.tokenizer.eos\_token\_id,  
            bos\_token\_id=self.tokenizer.bos\_token\_id,  
            eos\_token\_id=self.tokenizer.eos\_token\_id,  
            max\_new\_tokens=1024,  
            do\_sample=False,  
            use\_cache=True  
        )

    answer \= self.tokenizer.decode(outputs.cpu().tolist(), skip\_special\_tokens=True)  
      
    \# Extract only the JSON part  
    try:  
        json\_part \= answer\[answer.find('{'):answer.rfind('}')+1\]  
        return json\_part  
    except Exception:  
        return answer

# **Instantiate the service**

analysis\_service \= AnalysisService()

\*\*\`app/api/router.py\`\*\*  
\`\`\`python  
from fastapi import APIRouter, File, Form, UploadFile, HTTPException, Depends  
from fastapi.responses import JSONResponse  
import json

from. import schemas  
from..services.analysis\_service import analysis\_service, AnalysisService

router \= APIRouter(prefix="/api", tags=\["Analysis"\])

\# Dependency to get the singleton service instance  
def get\_analysis\_service():  
    return analysis\_service

@router.post(  
    "/analyze",  
    response\_model=schemas.InstagramProfileAnalysis,  
    summary="Analyze Instagram Profile Screenshot"  
)  
async def analyze\_profile(  
    image: UploadFile \= File(..., description="The Instagram profile screenshot to analyze."),  
    instructions: str \= Form(..., description="The prompt or instructions for the analysis."),  
    service: AnalysisService \= Depends(get\_analysis\_service)  
):  
    """  
    Analyzes an Instagram profile screenshot to determine if it's a service provider  
    and extracts key information.  
    """  
    if not image.content\_type.startswith("image/"):  
        raise HTTPException(status\_code=400, detail="Invalid file type. Only images are allowed.")

    image\_bytes \= await image.read()  
      
    try:  
        json\_string\_output \= service.run\_analysis(image\_bytes, instructions)  
          
        \# Parse the JSON string from the model  
        result\_dict \= json.loads(json\_string\_output)  
          
        \# Validate with Pydantic model before returning  
        validated\_result \= schemas.InstagramProfileAnalysis.model\_validate(result\_dict)  
          
        return validated\_result  
      
    except json.JSONDecodeError:  
        raise HTTPException(  
            status\_code=500,   
            detail="Failed to parse valid JSON from the model's response."  
        )  
    except Exception as e:  
        raise HTTPException(status\_code=500, detail=f"An unexpected error occurred: {str(e)}")

**app/main.py**

Python

from fastapi import FastAPI  
from.api import router as api\_router

app \= FastAPI(  
    title="Instagram Profile Analyzer API",  
    description="An API to analyze Instagram profile screenshots using DeepSeek-VL.",  
    version="1.0.0"  
)

app.include\_router(api\_router.router)

@app.get("/", tags=)  
async def read\_root():  
    return {"message": "Welcome to the Instagram Profile Analyzer API. Visit /docs for documentation."}

### **Launching the Service with Docker Compose**

While docker run is effective, docker-compose provides a more declarative and manageable way to define and run the service, especially as complexity grows.35

**docker-compose.yml**

YAML

version: '3.8'

services:  
  api:  
    build:  
      context:.  
      dockerfile: Dockerfile  
    ports:  
      \- "8000:8000"  
    volumes:  
      \# Use this for development to sync code changes without rebuilding  
      \# \-./app:/home/<USER>/app  
    environment:  
      \# Pass environment variables from.env file  
      \- HUGGING\_FACE\_HUB\_TOKEN=${HUGGING\_FACE\_HUB\_TOKEN}  
    deploy:  
      resources:  
        reservations:  
          devices:  
            \- driver: nvidia  
              count: 1  
              capabilities: \[gpu\]

To launch the service, simply run docker-compose up \--build from the project's root directory.

### **Interacting with the API: curl and Python requests Examples**

Once the service is running, it can be tested using various HTTP clients.

curl Example  
For a quick command-line test, use curl. This command sends an image file and the instruction text as multipart/form-data.36

Bash

curl \-X POST "http://localhost:8000/api/analyze" \\  
\-H "accept: application/json" \\  
\-F "image=@/path/to/your/instagram\_screenshot.png;type=image/png" \\  
\-F "instructions=Analyze this profile for beauty services."

Python requests Example  
For programmatic access or integration into other applications, the Python requests library is ideal. The following script demonstrates how to construct and send the same request.19

Python

import requests  
import json

\# \--- Configuration \---  
API\_URL \= "http://localhost:8000/api/analyze"  
IMAGE\_PATH \= "/path/to/your/instagram\_screenshot.png"  
INSTRUCTIONS \= "Is this a service provider for hair styling? Extract their details."

\# \--- Prepare the request \---  
\# The 'files' parameter handles multipart/form-data encoding  
files \= {  
    'image': (IMAGE\_PATH.split('/')\[-1\], open(IMAGE\_PATH, 'rb'), 'image/png')  
}

\# The 'data' parameter holds other form fields  
data \= {  
    'instructions': INSTRUCTIONS  
}

\# \--- Send the request \---  
try:  
    response \= requests.post(API\_URL, files=files, data=data)  
    response.raise\_for\_status()  \# Raise an exception for bad status codes (4xx or 5xx)

    \# \--- Process the response \---  
    print("Status Code:", response.status\_code)  
    print("Response JSON:")  
    print(json.dumps(response.json(), indent=2))

except requests.exceptions.RequestException as e:  
    print(f"An error occurred: {e}")

This script reads the image file in binary mode, packages it along with the instructions, sends the POST request, and prints the formatted JSON response from the API.

## **Conclusion and Future Enhancements**

This report has provided an exhaustive, step-by-step guide to building a sophisticated, GPU-accelerated vision-language API. The resulting architecture leverages a scalable FastAPI project structure, containerized within an optimized multi-stage Docker image. The core of the service is the deepseek-vl-7b-chat model, made efficient for local deployment through 4-bit quantization and accelerated with Flash Attention 2\. Critically, a robust few-shot prompting strategy was implemented to ensure the model reliably extracts and returns structured JSON data, fulfilling the primary objective.

The final system is a powerful and specialized tool capable of analyzing visual content from Instagram profiles and transforming it into actionable, structured information. The provided code, configurations, and testing methods constitute a complete, end-to-end solution.

While the current implementation is production-ready for many use cases, several avenues for future enhancement exist:

* **Batch Processing:** To improve throughput for analyzing multiple images, the API could be extended to accept a list of images in a single request. The analysis service would then process these as a batch on the GPU, which is significantly more efficient than processing them one by one.  
* **Asynchronous Inference:** For scenarios with high request concurrency, the synchronous model inference call can potentially block the server's main event loop. To improve responsiveness, the inference task could be offloaded to a separate thread pool (e.g., using asyncio.to\_thread) or a dedicated background task queue like Celery. This would allow the server to handle other incoming requests while a long-running analysis is in progress.9  
* **Transition to a Dedicated Serving Engine:** As discussed, for true high-performance, high-concurrency production environments, migrating the model serving component from the transformers library to a specialized engine like vLLM would be the next logical step. This would unlock superior throughput and provide more robust mechanisms for enforcing structured outputs.33  
* **Dynamic Prompt Management:** The current few-shot prompt is hardcoded in the service. A more flexible system might load prompts from a configuration file or a database. This would allow for easier updates, A/B testing of different prompt strategies, and adaptation to new analysis tasks without requiring code changes.

#### **Works cited**

1. How to Install PyTorch on the GPU with Docker | Saturn Cloud Blog, accessed July 18, 2025, [https://saturncloud.io/blog/how-to-install-pytorch-on-the-gpu-with-docker/](https://saturncloud.io/blog/how-to-install-pytorch-on-the-gpu-with-docker/)  
2. NVIDIA/nvidia-container-toolkit: Build and run containers leveraging NVIDIA GPUs \- GitHub, accessed July 18, 2025, [https://github.com/NVIDIA/nvidia-container-toolkit](https://github.com/NVIDIA/nvidia-container-toolkit)  
3. NVIDIA Container Toolkit, accessed July 18, 2025, [https://unrealcontainers.com/docs/concepts/nvidia-docker](https://unrealcontainers.com/docs/concepts/nvidia-docker)  
4. Overview — NVIDIA Container Toolkit \- NVIDIA Docs Hub, accessed July 18, 2025, [https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/overview.html](https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/overview.html)  
5. A Beginner's Guide to NVIDIA Container Toolkit on Docker | by Umberto Junior Mele, accessed July 18, 2025, [https://medium.com/@u.mele.coding/a-beginners-guide-to-nvidia-container-toolkit-on-docker-92b645f92006](https://medium.com/@u.mele.coding/a-beginners-guide-to-nvidia-container-toolkit-on-docker-92b645f92006)  
6. How to Structure Your FastAPI Projects \- Medium, accessed July 18, 2025, [https://medium.com/@amirm.lavasani/how-to-structure-your-fastapi-projects-0219a6600a8f](https://medium.com/@amirm.lavasani/how-to-structure-your-fastapi-projects-0219a6600a8f)  
7. Structuring a FastAPI Project: Best Practices \- DEV Community, accessed July 18, 2025, [https://dev.to/mohammad222pr/structuring-a-fastapi-project-best-practices-53l6](https://dev.to/mohammad222pr/structuring-a-fastapi-project-best-practices-53l6)  
8. FastAPI Best Practices and Conventions we used at our startup \- GitHub, accessed July 18, 2025, [https://github.com/zhanymkanov/fastapi-best-practices](https://github.com/zhanymkanov/fastapi-best-practices)  
9. How to Deploy FastAPI Applications with GPU Access in the Cloud, accessed July 18, 2025, [https://www.runpod.io/articles/guides/deploy-fastapi-applications-gpu-cloud](https://www.runpod.io/articles/guides/deploy-fastapi-applications-gpu-cloud)  
10. Tutorial on Fast API Form Data \- Orchestra, accessed July 18, 2025, [https://www.getorchestra.io/guides/tutorial-on-fast-api-form-data](https://www.getorchestra.io/guides/tutorial-on-fast-api-form-data)  
11. Uploading Files Using FastAPI: A Complete Guide to Secure File Handling \- Better Stack, accessed July 18, 2025, [https://betterstack.com/community/guides/scaling-python/uploading-files-using-fastapi/](https://betterstack.com/community/guides/scaling-python/uploading-files-using-fastapi/)  
12. Request Files \- FastAPI, accessed July 18, 2025, [https://fastapi.tiangolo.com/tutorial/request-files/](https://fastapi.tiangolo.com/tutorial/request-files/)  
13. Form Data \- FastAPI, accessed July 18, 2025, [https://fastapi.tiangolo.com/tutorial/request-forms/](https://fastapi.tiangolo.com/tutorial/request-forms/)  
14. deepseek-ai/deepseek-vl-7b-chat · Hugging Face, accessed July 18, 2025, [https://huggingface.co/deepseek-ai/deepseek-vl-7b-chat](https://huggingface.co/deepseek-ai/deepseek-vl-7b-chat)  
15. Bitsandbytes \- Hugging Face, accessed July 18, 2025, [https://huggingface.co/docs/transformers/main/quantization/bitsandbytes](https://huggingface.co/docs/transformers/main/quantization/bitsandbytes)  
16. GPU inference \- Hugging Face, accessed July 18, 2025, [https://huggingface.co/docs/transformers/v4.39.0/perf\_infer\_gpu\_one](https://huggingface.co/docs/transformers/v4.39.0/perf_infer_gpu_one)  
17. Dao-AILab/flash-attention: Fast and memory-efficient exact attention \- GitHub, accessed July 18, 2025, [https://github.com/Dao-AILab/flash-attention](https://github.com/Dao-AILab/flash-attention)  
18. Tutorial \- Pillow (PIL Fork) 11.3.0 documentation, accessed July 18, 2025, [https://pillow.readthedocs.io/en/stable/handbook/tutorial.html](https://pillow.readthedocs.io/en/stable/handbook/tutorial.html)  
19. Image Upload Code Sample \- Python with Requests, accessed July 18, 2025, [https://x-series-api.lightspeedhq.com/docs/products\_image\_uploads\_code\_sample\_python\_requests](https://x-series-api.lightspeedhq.com/docs/products_image_uploads_code_sample_python_requests)  
20. Uploading Images with Python Requests | ProxiesAPI, accessed July 18, 2025, [https://proxiesapi.com/articles/uploading-images-with-python-requests](https://proxiesapi.com/articles/uploading-images-with-python-requests)  
21. How to reduce python Docker image size \- Stack Overflow, accessed July 18, 2025, [https://stackoverflow.com/questions/78105348/how-to-reduce-python-docker-image-size](https://stackoverflow.com/questions/78105348/how-to-reduce-python-docker-image-size)  
22. Flash Attention \- Hugging Face, accessed July 18, 2025, [https://huggingface.co/docs/text-generation-inference/conceptual/flash\_attention](https://huggingface.co/docs/text-generation-inference/conceptual/flash_attention)  
23. Flash attention has no effect on inference \- Transformers \- Hugging Face Forums, accessed July 18, 2025, [https://discuss.huggingface.co/t/flash-attention-has-no-effect-on-inference/73453](https://discuss.huggingface.co/t/flash-attention-has-no-effect-on-inference/73453)  
24. Essential Pil (Pillow) Image Tutorial (For Machine Learning People) \- neptune.ai, accessed July 18, 2025, [https://neptune.ai/blog/pil-image-tutorial-for-machine-learning](https://neptune.ai/blog/pil-image-tutorial-for-machine-learning)  
25. Python Pillow Tutorial \- GeeksforGeeks, accessed July 18, 2025, [https://www.geeksforgeeks.org/python/python-pillow-tutorial/](https://www.geeksforgeeks.org/python/python-pillow-tutorial/)  
26. Vision Language Model Prompt Engineering Guide for Image and ..., accessed July 18, 2025, [https://developer.nvidia.com/blog/vision-language-model-prompt-engineering-guide-for-image-and-video-understanding/](https://developer.nvidia.com/blog/vision-language-model-prompt-engineering-guide-for-image-and-video-understanding/)  
27. Common Practices for prompting with the goal to extract data from the model output \- Reddit, accessed July 18, 2025, [https://www.reddit.com/r/PromptEngineering/comments/1bzysi7/common\_practices\_for\_prompting\_with\_the\_goal\_to/](https://www.reddit.com/r/PromptEngineering/comments/1bzysi7/common_practices_for_prompting_with_the_goal_to/)  
28. Shot-Based Prompting: Zero-Shot, One-Shot, and Few-Shot Prompting, accessed July 18, 2025, [https://learnprompting.org/docs/basics/few\_shot](https://learnprompting.org/docs/basics/few_shot)  
29. JindongGu/Awesome-Prompting-on-Vision-Language-Model \- GitHub, accessed July 18, 2025, [https://github.com/JindongGu/Awesome-Prompting-on-Vision-Language-Model](https://github.com/JindongGu/Awesome-Prompting-on-Vision-Language-Model)  
30. Prompt Engineering: How to get open source LLMs to just return a single value or JSON output? : r/LocalLLaMA \- Reddit, accessed July 18, 2025, [https://www.reddit.com/r/LocalLLaMA/comments/14zei4q/prompt\_engineering\_how\_to\_get\_open\_source\_llms\_to/](https://www.reddit.com/r/LocalLLaMA/comments/14zei4q/prompt_engineering_how_to_get_open_source_llms_to/)  
31. Effective Prompt Engineering for Data Extraction with Large Language Models \- Medium, accessed July 18, 2025, [https://medium.com/@kofsitho/effective-prompt-engineering-for-data-extraction-with-large-language-models-331ee454cbae](https://medium.com/@kofsitho/effective-prompt-engineering-for-data-extraction-with-large-language-models-331ee454cbae)  
32. TheBloke/deepseek-llm-7B-chat-GGUF \- Hugging Face, accessed July 18, 2025, [https://huggingface.co/TheBloke/deepseek-llm-7B-chat-GGUF](https://huggingface.co/TheBloke/deepseek-llm-7B-chat-GGUF)  
33. Supported Models \- vLLM, accessed July 18, 2025, [https://docs.vllm.ai/en/latest/models/supported\_models.html](https://docs.vllm.ai/en/latest/models/supported_models.html)  
34. Structured Outputs \- vLLM, accessed July 18, 2025, [https://docs.vllm.ai/en/latest/features/structured\_outputs.html](https://docs.vllm.ai/en/latest/features/structured_outputs.html)  
35. CaptainEureka/pytorch\_fastapi\_docker: Personal Exercise to understand the deployment of PyTorch models with FastAPI and Docker Compose \- GitHub, accessed July 18, 2025, [https://github.com/CaptainEureka/pytorch\_fastapi\_docker](https://github.com/CaptainEureka/pytorch_fastapi_docker)  
36. Sending files using curl \- TinEye APIs, accessed July 18, 2025, [https://help.tineye.com/article/216-sending-files-using-curl](https://help.tineye.com/article/216-sending-files-using-curl)  
37. How to Send POST Requests With cURL \- Oxylabs, accessed July 18, 2025, [https://oxylabs.io/blog/curl-post-requests](https://oxylabs.io/blog/curl-post-requests)