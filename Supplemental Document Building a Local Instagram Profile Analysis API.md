### **Supplemental Document: Building a Local Instagram Profile Analysis API**

This document provides a detailed guide for creating a local, containerized API that uses the `deepseek-vl-7b-chat` model to analyze Instagram profile screenshots.

-----

### 1\. Project Overview 📝

The goal is to build a high-performance API that can receive an image of an Instagram profile and determine if it belongs to a service provider. If it does, the API will extract key information like their username, location, services offered, and contact details, returning the data in a structured JSON format. The entire application will be encapsulated within a Docker container for portability and easy deployment on GPU-enabled hardware.

### 2\. Prerequisites

  * **Hardware:** An NVIDIA GPU with at least 10GB of VRAM is recommended to run the 7B parameter model, even with quantization.
  * **Software:**
      * **Docker:** The containerization platform.
      * **NVIDIA Container Toolkit:** To enable GPU access within Docker containers.
      * **Python 3.9+**

### 3\. Recommended Project Structure

A clean project structure is crucial for maintainability.

```
/instagram-analyzer-api
|-- app/
|   |-- __init__.py
|   |-- main.py             # FastAPI application logic
|   |-- model_loader.py     # Code to load and manage the VL model
|   |-- schemas.py          # Pydantic models for response structure
|-- Dockerfile
|-- requirements.txt
|-- .dockerignore
```

-----

### 4\. Step-by-Step Implementation Guide

#### **Step 1: The Docker Environment (`Dockerfile`)** 🐳

The `Dockerfile` defines the environment. We'll use an official NVIDIA CUDA image as our base to ensure all GPU drivers and libraries are available.

```dockerfile
# Use an NVIDIA CUDA base image with Python
FROM nvidia/cuda:12.1.1-devel-ubuntu22.04

# Set environment variables to prevent interactive prompts during installation
ENV TZ=Etc/UTC
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1

# Install Python, pip, and Git
RUN apt-get update && apt-get install -y \
    python3.10 python3-pip git build-essential && \
    rm -rf /var/lib/apt/lists/*

# Create a working directory
WORKDIR /app

# Copy requirements file and install dependencies
COPY requirements.txt .
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy the application code into the container
COPY ./app /app

# Expose the port the API will run on
EXPOSE 8000

# Command to run the FastAPI application with Uvicorn
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### **Step 2: Dependencies (`requirements.txt`)**

This file lists all the Python packages needed for the project. Using specific versions ensures reproducibility.

```
fastapi
uvicorn[standard]
python-multipart
torch
transformers
bitsandbytes
accelerate
Pillow
```

#### **Step 3: Loading the Model (`app/model_loader.py`)** 🧠

To avoid reloading the massive model with every API call, we'll use a singleton pattern or a simple global loader. This module will handle loading the model with 4-bit quantization and enabling **Flash Attention 2** for optimized performance.

```python
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig

class ModelLoader:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ModelLoader, cls).__new__(cls)
            cls._instance.model, cls._instance.tokenizer = cls._instance.load_model()
        return cls._instance

    def load_model(self):
        model_id = "deepseek-ai/deepseek-vl-7b-chat"
        
        # Configure 4-bit quantization to reduce memory footprint
        quantization_config = BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_compute_dtype=torch.float16
        )

        tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
        
        # Load the model with quantization and enable Flash Attention 2
        model = AutoModelForCausalLM.from_pretrained(
            model_id,
            trust_remote_code=True,
            quantization_config=quantization_config,
            attn_implementation="flash_attention_2", # Use Flash Attention
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        print("✅ Model and Tokenizer loaded successfully.")
        return model, tokenizer

# Instantiate the loader
model_singleton = ModelLoader()
```

#### **Step 4: Defining the Response Structure (`app/schemas.py`)**

Using Pydantic schemas helps enforce the structure of your API's output.

```python
from pydantic import BaseModel, Field
from typing import List, Optional

class ServiceProviderInfo(BaseModel):
    is_service_provider: bool = Field(..., description="True if the profile is a service provider, otherwise False.")
    username: Optional[str] = Field(None, description="The Instagram username (e.g., @username).")
    provider_type: Optional[str] = Field(None, description="Either 'Individual' or 'Business'.")
    location: Optional[str] = Field(None, description="City, State, or general area mentioned in the bio.")
    email: Optional[str] = Field(None, description="Contact email address, if available.")
    booking_link: Optional[str] = Field(None, description="The URL for booking appointments.")
    services: Optional[List[str]] = Field([], description="A list of services offered.")
    raw_bio_text: Optional[str] = Field(None, description="The full text of the bio.")
```

#### **Step 5: The FastAPI Application (`app/main.py`)** 🚀

This is the core of the API. It defines the endpoint, handles the image upload, preprocesses the image, and orchestrates the call to the model.

```python
from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from PIL import Image
import io
import json

from .model_loader import model_singleton
from .schemas import ServiceProviderInfo

app = FastAPI(title="Instagram Profile Analyzer API")

# The prompt is the intellectual core of this service.
# It guides the model to perform classification and extraction in one pass.
ANALYSIS_PROMPT_TEMPLATE = """
Analyze the provided Instagram profile screenshot. Your task is to first classify if the account belongs to a service provider (e.g., aesthetics, beauty, wellness, coaching). Then, extract the specified information. Respond ONLY with a valid JSON object that adheres to the following schema. Do not include any text or markdown formatting before or after the JSON.

JSON Schema:
{
  "is_service_provider": "boolean",
  "username": "string | null",
  "provider_type": "string ('Individual' or 'Business') | null",
  "location": "string | null",
  "email": "string | null",
  "booking_link": "string | null",
  "services": "array of strings | null",
  "raw_bio_text": "string | null"
}

If the account is not a service provider, set `is_service_provider` to false and leave the other fields null.

Here are the details to extract if it IS a service provider:
- **username**: The handle, starting with '@'.
- **provider_type**: Infer if it's an individual or a business entity.
- **location**: Any mentioned city, state, or neighborhood.
- **email**: The contact email.
- **booking_link**: Any URL for booking/appointments (e.g., linkin.bio, acuity, calendly).
- **services**: A list of specific services mentioned (e.g., "Lip Fillers", "Lash Extensions", "Microblading").
- **raw_bio_text**: The complete, unedited text from the bio.

Now, analyze the image and provide the JSON output.
"""

@app.post("/analyze-profile/", response_model=ServiceProviderInfo)
async def analyze_profile(
    image: UploadFile = File(...),
    prompt: str = Form(ANALYSIS_PROMPT_TEMPLATE)
):
    if not image.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="File provided is not an image.")

    try:
        # Load the model and tokenizer from our singleton
        model = model_singleton.model
        tokenizer = model_singleton.tokenizer

        # Read and prepare the image
        image_bytes = await image.read()
        pil_image = Image.open(io.BytesIO(image_bytes)).convert("RGB")

        # Prepare the input for the model
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "image", "image": pil_image},
                    {"type": "text", "text": prompt}
                ]
            }
        ]
        
        inputs = tokenizer.apply_chat_template(messages, add_generation_prompt=True, return_tensors="pt").to(model.device)
        
        # Generate the response
        gen_kwargs = {"max_new_tokens": 1024, "do_sample": False}
        outputs = model.generate(inputs, **gen_kwargs)
        response_text = tokenizer.decode(outputs[0], skip_special_tokens=True)

        # Extract the JSON part of the response
        # The model output may contain extra text, so we find the JSON block.
        json_start = response_text.find('{')
        json_end = response_text.rfind('}') + 1
        if json_start == -1 or json_end == 0:
            raise HTTPException(status_code=500, detail="Failed to get a valid JSON response from the model.")
        
        json_str = response_text[json_start:json_end]
        parsed_json = json.loads(json_str)

        return ServiceProviderInfo(**parsed_json)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

```

-----

### 5\. Deployment and Testing

#### **Step 1: Build the Docker Image**

From the root directory (`/instagram-analyzer-api`), run:

```bash
docker build -t instagram-analyzer .
```

#### **Step 2: Run the Docker Container**

To run the container and give it access to your NVIDIA GPU, use the `--gpus all` flag.

```bash
docker run -d --gpus all -p 8000:8000 --name ig-analyzer-api instagram-analyzer
```

Your API should now be running and accessible at `http://localhost:8000`.

#### **Step 3: Test the API**

You can test the endpoint using `curl` or a Python script.

**Using `curl`:**

Save an Instagram screenshot as `profile_screenshot.png` in your current directory.

```bash
curl -X POST -F "image=@profile_screenshot.png" http://localhost:8000/analyze-profile/
```

**Using Python `requests`:**

```python
import requests
import json

# The path to your screenshot
image_path = 'profile_screenshot.png'
api_url = 'http://localhost:8000/analyze-profile/'

with open(image_path, 'rb') as f:
    files = {'image': (image_path, f, 'image/png')}
    response = requests.post(api_url, files=files)

print(f"Status Code: {response.status_code}")
if response.status_code == 200:
    print("Response JSON:")
    print(json.dumps(response.json(), indent=2))
else:
    print("Error:", response.text)
```

This will call your local API, which will process the image and return a structured JSON object with the extracted information.

-----

-----

### **Prompt for Augment Code Agent**

Here is a detailed prompt you can provide to an AI code augmentation agent to generate the code for this project.

**Role:** You are an expert AI/ML engineer specializing in deploying Vision-Language Models (VLMs) in production environments.

**Project Goal:**
Generate the complete Python code and configuration files for a local, Dockerized FastAPI application. The application will use the `deepseek-ai/deepseek-vl-7b-chat` model to analyze screenshots of Instagram profiles. Its primary function is to identify service providers and extract key business information into a structured JSON format.

**Key Technologies:**

  * **Containerization:** Docker
  * **Web Framework:** FastAPI
  * **ML/AI Libraries:** PyTorch, Hugging Face `transformers`
  * **Model Optimization:** `bitsandbytes` (for quantization), Flash Attention 2
  * **Image Processing:** Pillow

-----

**Core Requirements:**

1.  **`Dockerfile`:**

      * Use the `nvidia/cuda:12.1.1-devel-ubuntu22.04` base image.
      * Install `python3.10`, `pip`, and `git`.
      * Copy `requirements.txt` and install all dependencies.
      * Copy the application source code from an `app` directory.
      * Expose port `8000`.
      * Use `uvicorn` to run the FastAPI app, making it available on all network interfaces.

2.  **`requirements.txt`:**

      * Create a `requirements.txt` file containing the following packages: `fastapi`, `uvicorn[standard]`, `python-multipart`, `torch`, `transformers`, `bitsandbytes`, `accelerate`, and `Pillow`.

3.  **Project Structure:**

      * Organize the code into the following structure:
        ```
        /
        |-- app/
        |   |-- __init__.py
        |   |-- main.py
        |   |-- model_loader.py
        |   |-- schemas.py
        |-- Dockerfile
        |-- requirements.txt
        ```

4.  **Model Loading (`app/model_loader.py`):**

      * Implement a `ModelLoader` class that acts as a singleton. It should load the model and tokenizer only once.
      * Load the `deepseek-ai/deepseek-vl-7b-chat` model from Hugging Face.
      * Configure the model to use **4-bit quantization** via `BitsAndBytesConfig` (`load_in_4bit=True`).
      * Enable **Flash Attention 2** by setting `attn_implementation="flash_attention_2"` during model loading for maximum performance.
      * The model should be loaded with `torch_dtype=torch.float16` and `device_map="auto"`.

5.  **Pydantic Schemas (`app/schemas.py`):**

      * Define a Pydantic `BaseModel` named `ServiceProviderInfo`.
      * This model must contain the following fields with appropriate types and descriptions: `is_service_provider` (bool), `username` (Optional[str]), `provider_type` (Optional[str]), `location` (Optional[str]), `email` (Optional[str]), `booking_link` (Optional[str]), `services` (Optional[List[str]]), and `raw_bio_text` (Optional[str]).

6.  **FastAPI Application (`app/main.py`):**

      * Create a FastAPI app instance.

      * Implement a single POST endpoint at `/analyze-profile/`.

      * The endpoint must accept `multipart/form-data` containing an `image` (`UploadFile`) and a `prompt` (`Form`).

      * Use the following text as the default value for the `prompt` parameter. This prompt is critical for guiding the model's output.

        ```python
        ANALYSIS_PROMPT_TEMPLATE = """
        Analyze the provided Instagram profile screenshot. Your task is to first classify if the account belongs to a service provider (e.g., aesthetics, beauty, wellness, coaching). Then, extract the specified information. Respond ONLY with a valid JSON object that adheres to the following schema. Do not include any text or markdown formatting before or after the JSON.

        JSON Schema:
        {
          "is_service_provider": "boolean",
          "username": "string | null",
          "provider_type": "string ('Individual' or 'Business') | null",
          "location": "string | null",
          "email": "string | null",
          "booking_link": "string | null",
          "services": "array of strings | null",
          "raw_bio_text": "string | null"
        }

        If the account is not a service provider, set `is_service_provider` to false and leave the other fields null. Now, analyze the image and provide the JSON output.
        """
        ```

      * **Processing Logic:**

          * Validate that the uploaded file is an image.
          * Use the `model_loader` singleton to access the pre-loaded model and tokenizer.
          * Read the image using `Pillow`, ensuring it's in RGB format.
          * Prepare the inputs for the model using the `tokenizer.apply_chat_template` method, combining the image and the prompt.
          * Generate the model's response.
          * **Crucially, parse the model's raw text output to reliably extract the JSON block.** The model might add conversational text before or after the JSON. Your code must robustly find and parse the JSON.
          * Instantiate and return the `ServiceProviderInfo` Pydantic model.

      * **Error Handling:** Include a `try...except` block to catch potential errors during model inference or JSON parsing and return an appropriate `HTTPException`.

**Final Output:**
Please provide the complete, ready-to-use code for the following files: `Dockerfile`, `requirements.txt`, `app/main.py`, `app/model_loader.py`, and `app/schemas.py`. Ensure the code is well-commented and follows best practices.